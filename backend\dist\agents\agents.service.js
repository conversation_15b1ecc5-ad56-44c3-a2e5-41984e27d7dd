"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let AgentsService = class AgentsService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async findAll(page = 1, limit = 10) {
        const agents = await this.prisma.agent.findMany({
            skip: (page - 1) * limit,
            take: limit,
            orderBy: {
                createdAt: 'desc',
            },
            include: {
                author: true,
            },
        });
        return agents.map(agent => ({
            id: agent.id,
            name: agent.name,
            description: agent.description,
            imageUrl: agent.imageUrl,
            avatarUrl: agent.avatarUrl,
            views: agent.views > 10000 ? `${Math.floor(agent.views / 10000)}万` : (agent.views || 0).toString(),
            author: agent.author?.nickname || '匿名',
            tags: agent.tags || [],
            openingLine: agent.openingLine,
        }));
    }
    async findOne(id) {
        const agent = await this.prisma.agent.findUnique({
            where: { id },
            include: {
                author: true,
                stories: true,
            },
        });
        if (!agent) {
            return null;
        }
        return {
            id: agent.id,
            name: agent.name,
            description: agent.description,
            imageUrl: agent.imageUrl,
            avatarUrl: agent.avatarUrl,
            openingLine: agent.openingLine,
            views: (agent.views || 0) > 10000 ? `${Math.floor((agent.views || 0) / 10000)}万` : (agent.views || 0).toString(),
            fans: (agent.fans || 0) > 10000 ? `${Math.floor((agent.fans || 0) / 10000)}万` : (agent.fans || 0).toString(),
            dialogues: agent.dialogues || 0,
            starValue: (agent.starValue || 0).toString(),
            tags: agent.tags || [],
            author: agent.author ? {
                id: agent.author.id,
                nickname: agent.author.nickname,
                avatarUrl: agent.author.avatarUrl || null,
            } : null,
            stories: agent.stories.map(story => ({
                id: story.id,
                title: story.title,
                description: story.description,
                plays: story.plays || 0,
            })),
        };
    }
    async create(createAgentData, authorId) {
        return this.prisma.agent.create({
            data: {
                ...createAgentData,
                authorId: authorId,
            },
        });
    }
    async createWithAI(prompt) {
        const user = await this.prisma.user.findFirst();
        if (!user) {
            throw new Error('无用户可关联，请先运行数据库种子脚本');
        }
        const aiGeneratedData = {
            name: `AI角色${Math.floor(Math.random() * 1000)}`,
            description: `基于"${prompt}"创建的AI角色，拥有独特的个性和魅力。`,
            tags: ['AI生成', '独特', '有趣'],
            imageUrl: 'https://i.imgur.com/Kbd0827.png',
            avatarUrl: 'https://i.imgur.com/Kbd0827.png',
            openingLine: '你好！很高兴遇见你，让我们开始一段有趣的对话吧！',
            authorId: user.id,
        };
        const newAgent = await this.prisma.agent.create({
            data: aiGeneratedData,
        });
        return {
            id: newAgent.id,
            name: newAgent.name,
            description: newAgent.description,
            imageUrl: newAgent.imageUrl,
            avatarUrl: newAgent.avatarUrl,
            openingLine: newAgent.openingLine,
            tags: newAgent.tags || [],
            views: '0',
            author: user.nickname,
        };
    }
};
exports.AgentsService = AgentsService;
exports.AgentsService = AgentsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], AgentsService);
//# sourceMappingURL=agents.service.js.map
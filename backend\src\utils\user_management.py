#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户管理工具模块
提供可复用的用户创建和管理功能
"""

import asyncio
from typing import Optional
from supabase import Client


async def get_or_create_user(supabase_admin: Client, email: str, password: str, **metadata) -> Optional[str]:
    """
    一个可复用的函数，用于幂等地创建用户并更新其档案。
    
    Args:
        supabase_admin: Supabase管理员客户端
        email: 用户邮箱
        password: 用户密码
        **metadata: 额外的用户元数据
        
    Returns:
        用户ID，如果失败返回None
    """
    try:
        # 检查用户是否存在
        response = await asyncio.to_thread(lambda: supabase_admin.auth.admin.list_users())
        users_list = getattr(response, 'users', response)
        existing_user = next((u for u in users_list if u.email == email), None)

        if existing_user:
            print(f"[USER_MGMT] ✓ 用户 '{email}' 已存在。")
            return str(existing_user.id)
            
    except Exception as e:
        print(f"[USER_MGMT] WARN: 检查用户存在性时发生错误: {e}")

    # 创建新用户
    try:
        print(f"[USER_MGMT] INFO: 正在创建新用户 '{email}'...")
        
        # 创建用户账户
        create_response = await asyncio.to_thread(
            lambda: supabase_admin.auth.admin.create_user({
                "email": email,
                "password": password,
                "email_confirm": True,
                "user_metadata": metadata
            })
        )
        
        if hasattr(create_response, 'user') and create_response.user:
            user_id = str(create_response.user.id)
            print(f"[USER_MGMT] ✓ 用户 '{email}' 创建成功，ID: {user_id}")
            return user_id
        else:
            print(f"[USER_MGMT] ERROR: 用户创建失败，响应格式异常")
            return None
            
    except Exception as e:
        print(f"[USER_MGMT] ERROR: 创建用户时发生错误: {e}")
        return None


async def create_user_if_not_exists(supabase_admin: Client, email: str, password: str, **metadata) -> Optional[str]:
    """
    向后兼容的函数名，调用get_or_create_user
    """
    return await get_or_create_user(supabase_admin, email, password, **metadata)

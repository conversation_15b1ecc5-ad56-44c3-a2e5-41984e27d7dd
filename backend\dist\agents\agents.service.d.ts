import { PrismaService } from '../prisma/prisma.service';
export declare class AgentsService {
    private prisma;
    constructor(prisma: PrismaService);
    findAll(page?: number, limit?: number): Promise<{
        id: string;
        name: string;
        description: string;
        imageUrl: string;
        avatarUrl: string;
        views: any;
        author: string;
        tags: any;
        openingLine: string;
    }[]>;
    findOne(id: string): Promise<{
        id: string;
        name: string;
        description: string;
        imageUrl: string;
        avatarUrl: string;
        openingLine: string;
        views: any;
        fans: any;
        dialogues: any;
        starValue: any;
        tags: any;
        author: {
            id: string;
            nickname: string | null;
            avatarUrl: any;
        } | null;
        stories: {
            id: string;
            title: string;
            description: string;
            plays: any;
        }[];
    } | null>;
    create(createAgentData: any, authorId: string): Promise<{
        id: string;
        name: string;
        description: string;
        tags: string[];
        imageUrl: string;
        avatarUrl: string;
        openingLine: string;
        views: number;
        fans: number;
        dialogues: number;
        starValue: bigint;
        authorId: string;
        createdAt: Date;
        updatedAt: Date;
    }>;
    createWithAI(prompt: string): Promise<{
        id: string;
        name: string;
        description: string;
        imageUrl: string;
        avatarUrl: string;
        openingLine: string;
        tags: any;
        views: string;
        author: string | null;
    }>;
}

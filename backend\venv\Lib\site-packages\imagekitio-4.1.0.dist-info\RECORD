imagekitio-4.1.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
imagekitio-4.1.0.dist-info/LICENSE,sha256=_VLMm68E4S-S7tDW0tH0tJDIc4Fx68EX3abf_AYvDzQ,1065
imagekitio-4.1.0.dist-info/METADATA,sha256=rbA0r3_mIYTRLZixjr6YP1OWcuwt89K588U_uA8kL5Q,51333
imagekitio-4.1.0.dist-info/RECORD,,
imagekitio-4.1.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
imagekitio-4.1.0.dist-info/WHEEL,sha256=eOLhNAGa2EW3wWl_TU484h7q1UNgy0JXjjoqKoxAAQc,92
imagekitio-4.1.0.dist-info/top_level.txt,sha256=KmfDDLOOA4ZCPxER7ewjxvWamoHM_kHOUQzodftvbDU,17
imagekitio/__init__.py,sha256=lPDl0ol6yictBJ9L6Z7wuUK3KOGLV9tq93BfR2Jc_HI,29
imagekitio/__pycache__/__init__.cpython-310.pyc,,
imagekitio/__pycache__/client.cpython-310.pyc,,
imagekitio/__pycache__/file.cpython-310.pyc,,
imagekitio/__pycache__/resource.cpython-310.pyc,,
imagekitio/__pycache__/url.cpython-310.pyc,,
imagekitio/client.py,sha256=qyfgbh957kGrr0qPNungZp_RuMf2llBvZdtUSYrMT-w,10958
imagekitio/constants/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
imagekitio/constants/__pycache__/__init__.cpython-310.pyc,,
imagekitio/constants/__pycache__/defaults.cpython-310.pyc,,
imagekitio/constants/__pycache__/errors.cpython-310.pyc,,
imagekitio/constants/__pycache__/files.cpython-310.pyc,,
imagekitio/constants/__pycache__/supported_transform.cpython-310.pyc,,
imagekitio/constants/__pycache__/url.cpython-310.pyc,,
imagekitio/constants/defaults.py,sha256=UwE9ti7I0WJ6s-2UHiY8X6YH0DVC5PwhULY3ArdiQNE,574
imagekitio/constants/errors.py,sha256=Ji2JmoVNd1Pgsw9iAn9XSD-Myg16s-SjB5W033pNeiw,2972
imagekitio/constants/files.py,sha256=H-************************************vQYDA,620
imagekitio/constants/supported_transform.py,sha256=z3kmSWgqwo3zlpVmOnbDZON9yOsEAli01edah1f6g8o,734
imagekitio/constants/url.py,sha256=NKP8ABdG_ILjkf4fzMs370JkT2v6aN8sen9o8eRIbwo,155
imagekitio/exceptions/BadRequestException.py,sha256=bAHUVBpZSvy1atPnV0zem0queF98hL1UIh_5J4u__gk,446
imagekitio/exceptions/ConflictException.py,sha256=PovXybi3DlsD94iSH-zdkP16YVyzU7sOGtjzK7N0TlE,368
imagekitio/exceptions/ForbiddenException.py,sha256=FYfBtTceZ4Oe30mWgm0TCJck1a_ToXPOU4uVIe5QYv0,369
imagekitio/exceptions/InternalServerException.py,sha256=RIviuEXrKmrAof3k1dJk_Xg6beo4yOSf1xfB8rSOb98,374
imagekitio/exceptions/NotFoundException.py,sha256=01Ux09nwt6czhXqm_ga-hzK6FC88PjGhydVVZmYAuP8,368
imagekitio/exceptions/PartialSuccessException.py,sha256=H8pk_t94mXFajMnl-E3g89xrb31_CWaV670Ejj58_2s,374
imagekitio/exceptions/TooManyRequestsException.py,sha256=RvY0beltKulDU0HtK4zGbr1mAUEn1zap4m3OVQAr308,375
imagekitio/exceptions/UnauthorizedException.py,sha256=rOdxBpv0ENW0fNv-a0PIM6cBbCVLXwkLWQcPzhg62lc,372
imagekitio/exceptions/UnknownException.py,sha256=kzmVL-HeNRhc48r0cpubqHlBs_hNzxbfe7GSEPtkPao,367
imagekitio/exceptions/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
imagekitio/exceptions/__pycache__/BadRequestException.cpython-310.pyc,,
imagekitio/exceptions/__pycache__/ConflictException.cpython-310.pyc,,
imagekitio/exceptions/__pycache__/ForbiddenException.cpython-310.pyc,,
imagekitio/exceptions/__pycache__/InternalServerException.cpython-310.pyc,,
imagekitio/exceptions/__pycache__/NotFoundException.cpython-310.pyc,,
imagekitio/exceptions/__pycache__/PartialSuccessException.cpython-310.pyc,,
imagekitio/exceptions/__pycache__/TooManyRequestsException.cpython-310.pyc,,
imagekitio/exceptions/__pycache__/UnauthorizedException.cpython-310.pyc,,
imagekitio/exceptions/__pycache__/UnknownException.cpython-310.pyc,,
imagekitio/exceptions/__pycache__/__init__.cpython-310.pyc,,
imagekitio/file.py,sha256=YIou7SkmM8A4zCOswnhndzbcsNQmaMWhHUYw6bz3xVg,37166
imagekitio/models/CopyFileRequestOptions.py,sha256=5oxv9EB_rYnOYnk-3r11W7xmo7lZXLRKLslBcBlij9s,486
imagekitio/models/CopyFolderRequestOptions.py,sha256=2UV0LzD_1JBihdlTfDuxBITCBVhlaLzXnkwKBixEnVA,496
imagekitio/models/CreateCustomMetadataFieldsRequestOptions.py,sha256=TFFs5-SlwH2OVPoC4GcoKlf5-nCLbEQOLIqasGSDvvU,1502
imagekitio/models/CreateFolderRequestOptions.py,sha256=iOMbkeYZ3qhDHzOs0Imb1nAFSdGMB-phQD5fN4GP85A,294
imagekitio/models/CustomMetaDataTypeEnum.py,sha256=YemvfONkABjyECi-f961Qj3uIwJzd-FgwI8sZZfOjw0,175
imagekitio/models/CustomMetadataFieldsSchema.py,sha256=a5RdS0vB7sgkWLuj4HpTRiWaALdMuGf9UnGDMhXsHfY,1027
imagekitio/models/DeleteFolderRequestOptions.py,sha256=a12oP--XSuBtNS6W4fa-4kSYFx7KKr_WiiMaPkb9qX0,162
imagekitio/models/ListAndSearchFileRequestOptions.py,sha256=zN1sELFm_88aEdwkXZZnixfmKNL7w9mPE_C0YxmB9I0,814
imagekitio/models/MoveFileRequestOptions.py,sha256=X7IOG_xiK2fCxgjCN0RfgKYw84L5_mm4AzoQQuA6URk,302
imagekitio/models/MoveFolderRequestOptions.py,sha256=p25BsbmtQjtIFo-xHNI0mtQhaA8HdvGvfYEjqf3g9kM,312
imagekitio/models/RenameFileRequestOptions.py,sha256=EtI7X6bqh5Yb0iSrmuABtaikQvCZf2kYmn2BB2mr4nQ,383
imagekitio/models/UpdateCustomMetadataFieldsRequestOptions.py,sha256=ygL5_Z0aAQi0dDPG2m3H95NYTc26bHBTfjper0-aJZs,1420
imagekitio/models/UpdateFileRequestOptions.py,sha256=O_myqjHTToZH6dq0aSRq1ASCzd6iszCQ8MUgi15y6J4,916
imagekitio/models/UploadFileRequestOptions.py,sha256=tTsWu-hbKiKk9EmRHc6tZnCP41xDzVP3q7Pmx08zKXs,2083
imagekitio/models/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
imagekitio/models/__pycache__/CopyFileRequestOptions.cpython-310.pyc,,
imagekitio/models/__pycache__/CopyFolderRequestOptions.cpython-310.pyc,,
imagekitio/models/__pycache__/CreateCustomMetadataFieldsRequestOptions.cpython-310.pyc,,
imagekitio/models/__pycache__/CreateFolderRequestOptions.cpython-310.pyc,,
imagekitio/models/__pycache__/CustomMetaDataTypeEnum.cpython-310.pyc,,
imagekitio/models/__pycache__/CustomMetadataFieldsSchema.cpython-310.pyc,,
imagekitio/models/__pycache__/DeleteFolderRequestOptions.cpython-310.pyc,,
imagekitio/models/__pycache__/ListAndSearchFileRequestOptions.cpython-310.pyc,,
imagekitio/models/__pycache__/MoveFileRequestOptions.cpython-310.pyc,,
imagekitio/models/__pycache__/MoveFolderRequestOptions.cpython-310.pyc,,
imagekitio/models/__pycache__/RenameFileRequestOptions.cpython-310.pyc,,
imagekitio/models/__pycache__/UpdateCustomMetadataFieldsRequestOptions.cpython-310.pyc,,
imagekitio/models/__pycache__/UpdateFileRequestOptions.cpython-310.pyc,,
imagekitio/models/__pycache__/UploadFileRequestOptions.cpython-310.pyc,,
imagekitio/models/__pycache__/__init__.cpython-310.pyc,,
imagekitio/models/results/AITags.py,sha256=gt-************************************bkuo,315
imagekitio/models/results/BulkDeleteFileResult.py,sha256=j_TU-pC0RgsRg5mu24dmb1ev93FRgfCLLipiiSHOGOA,639
imagekitio/models/results/CustomMetadataFieldsResult.py,sha256=4_R8RJ57QrN7nu96pPQZrIQPwEGd64M87qlaTkkb1bo,591
imagekitio/models/results/CustomMetadataFieldsResultWithResponseMetadata.py,sha256=ratmSL0W2giHHcz4g6CPZKNfn3ZUnUnWT4YiKxPydaw,604
imagekitio/models/results/CustomMetadataSchema.py,sha256=YwpBV8e6JvCzvkpeywSKAwghXdz3kZnD2FEliVAXJQQ,743
imagekitio/models/results/EmbeddedMetadata.py,sha256=P8So5bj4ZwbeGSvGoyHmFuAROpx645DQG9GCh7ikpTE,461
imagekitio/models/results/FileResult.py,sha256=HpKvgs9hX-AC01Jb0e6J1aEN5jhPTIZeuzl548CHCsY,2010
imagekitio/models/results/FileResultWithResponseMetadata.py,sha256=HLWnHqpozflgAn1BMvFLkKq4Z_fEDDLdmFwEQLpyngQ,1523
imagekitio/models/results/FolderResult.py,sha256=XF8sTtDsubjSmgApYlH7SakPMozvNCvZTvRSQSdhWqw,562
imagekitio/models/results/GetBulkJobStatusResult.py,sha256=7lGlFHB3Uj3WBx3fDu7IuKP4gbJEE0zDGOcPk4bPsA4,650
imagekitio/models/results/GetMetadataResult.py,sha256=uBqffFFFkbkD7MyDfgpC2Nv-nD4KtNBQDiaE1n78x-0,1547
imagekitio/models/results/ListCustomMetadataFieldsResult.py,sha256=9Afle2sO5rCAVu6DP-inyBDDfbs4qC8UOMJ6AEVMHcY,550
imagekitio/models/results/ListFileResult.py,sha256=92Jo13xB9I3g2g5U2GPnFEutQ7_TWnUIU7kjRGNggFc,492
imagekitio/models/results/MetadataExif.py,sha256=GJX8_WLuoG_dfeb_4lLqZc0401zSLNFq6EeSzU1JfT4,3401
imagekitio/models/results/MetadataExifExif.py,sha256=MTuvhj1rK8o9KbALwqQY62olHMsvXy-oSQpHqnn-aE4,2395
imagekitio/models/results/MetadataExifGPS.py,sha256=gOyac21V_Xhu0eU3NKu7bYJnLunGEaDW7f85KXPAaZk,424
imagekitio/models/results/MetadataExifImage.py,sha256=ggtM-ipsDiAhFeQDqrulfGj6qDx8zVYy1HcGZPROLdc,916
imagekitio/models/results/MetadataExifInteroperability.py,sha256=ltbGdP2f2QRUG45uYthglpzBrMU7Z53MmZ6RvS3dIpw,337
imagekitio/models/results/MetadataExifThumbnail.py,sha256=oaSYMjg6cjQ9ZAQgI3_4lY3ndYhHn12DlOteupnHAG4,658
imagekitio/models/results/PurgeCacheResult.py,sha256=UPRUNtO5Y5XoamV-S7hjU7au1ROgr6_YUTEHeKhWd_M,433
imagekitio/models/results/PurgeCacheStatusResult.py,sha256=ItiR6Dr4MSIW1Pqndu2rM_L68ZAW70x-1vPcfSziE_U,427
imagekitio/models/results/RenameFileResult.py,sha256=DcsuuHSrruIdV0MKkNglhjM9VUKqH5UjsIrNEWp5QgE,458
imagekitio/models/results/ResponseMetadata.py,sha256=vqK8Uh2b_PlJu8PzcdK2TmOz4FGRhqlImF75VNKMrKI,338
imagekitio/models/results/ResponseMetadataResult.py,sha256=uw57GCCXNBlWwadGSnhbg6YXUylPdbNJxAmdG0P8HN8,385
imagekitio/models/results/TagsResult.py,sha256=XL7K1CWwEFnzMUAeU0ok20l7rzMvB-yjrkj4p6_n2RA,624
imagekitio/models/results/UploadFileResult.py,sha256=Nqm9nA2IZjQc4OkSaVNWAcNbh6PRJeXfOsFdpWXW3MA,2706
imagekitio/models/results/VersionInfo.py,sha256=xO740QokKFCUtyDbGUwsfIZywyVraj_hQQtk10AMaxU,263
imagekitio/models/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
imagekitio/models/results/__pycache__/AITags.cpython-310.pyc,,
imagekitio/models/results/__pycache__/BulkDeleteFileResult.cpython-310.pyc,,
imagekitio/models/results/__pycache__/CustomMetadataFieldsResult.cpython-310.pyc,,
imagekitio/models/results/__pycache__/CustomMetadataFieldsResultWithResponseMetadata.cpython-310.pyc,,
imagekitio/models/results/__pycache__/CustomMetadataSchema.cpython-310.pyc,,
imagekitio/models/results/__pycache__/EmbeddedMetadata.cpython-310.pyc,,
imagekitio/models/results/__pycache__/FileResult.cpython-310.pyc,,
imagekitio/models/results/__pycache__/FileResultWithResponseMetadata.cpython-310.pyc,,
imagekitio/models/results/__pycache__/FolderResult.cpython-310.pyc,,
imagekitio/models/results/__pycache__/GetBulkJobStatusResult.cpython-310.pyc,,
imagekitio/models/results/__pycache__/GetMetadataResult.cpython-310.pyc,,
imagekitio/models/results/__pycache__/ListCustomMetadataFieldsResult.cpython-310.pyc,,
imagekitio/models/results/__pycache__/ListFileResult.cpython-310.pyc,,
imagekitio/models/results/__pycache__/MetadataExif.cpython-310.pyc,,
imagekitio/models/results/__pycache__/MetadataExifExif.cpython-310.pyc,,
imagekitio/models/results/__pycache__/MetadataExifGPS.cpython-310.pyc,,
imagekitio/models/results/__pycache__/MetadataExifImage.cpython-310.pyc,,
imagekitio/models/results/__pycache__/MetadataExifInteroperability.cpython-310.pyc,,
imagekitio/models/results/__pycache__/MetadataExifThumbnail.cpython-310.pyc,,
imagekitio/models/results/__pycache__/PurgeCacheResult.cpython-310.pyc,,
imagekitio/models/results/__pycache__/PurgeCacheStatusResult.cpython-310.pyc,,
imagekitio/models/results/__pycache__/RenameFileResult.cpython-310.pyc,,
imagekitio/models/results/__pycache__/ResponseMetadata.cpython-310.pyc,,
imagekitio/models/results/__pycache__/ResponseMetadataResult.cpython-310.pyc,,
imagekitio/models/results/__pycache__/TagsResult.cpython-310.pyc,,
imagekitio/models/results/__pycache__/UploadFileResult.cpython-310.pyc,,
imagekitio/models/results/__pycache__/VersionInfo.cpython-310.pyc,,
imagekitio/models/results/__pycache__/__init__.cpython-310.pyc,,
imagekitio/resource.py,sha256=JBgDUvpNaXgubL1ZIdONVo9WNDRxkUs1oGnSYklBO1c,2901
imagekitio/url.py,sha256=xzERIUkSNsQ-1FVXmrkaRHq992PF9fbjPBP4rBpI9u4,9137
imagekitio/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
imagekitio/utils/__pycache__/__init__.cpython-310.pyc,,
imagekitio/utils/__pycache__/calculation.cpython-310.pyc,,
imagekitio/utils/__pycache__/formatter.cpython-310.pyc,,
imagekitio/utils/__pycache__/utils.cpython-310.pyc,,
imagekitio/utils/calculation.py,sha256=quMoAJkUJXtZJTN78_HblOq89iUKN-0ZklsT-XBoV5Y,1116
imagekitio/utils/formatter.py,sha256=JKHl47m2vD3BSrKR1T6WvfO72DiUNKWBHS2oOVUSCkg,1327
imagekitio/utils/utils.py,sha256=Hn52BZ4AT0zkrJ4wOIP2utWBsPTpXwyjbRNWHSEWCxA,4137
tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
tests/__pycache__/__init__.cpython-310.pyc,,
tests/__pycache__/helpers.cpython-310.pyc,,
tests/__pycache__/test_client.cpython-310.pyc,,
tests/__pycache__/test_custom_metadata_fields_ops.cpython-310.pyc,,
tests/__pycache__/test_files_ops.cpython-310.pyc,,
tests/__pycache__/test_folder_ops.cpython-310.pyc,,
tests/__pycache__/test_generate_url.cpython-310.pyc,,
tests/__pycache__/test_models_results.cpython-310.pyc,,
tests/__pycache__/test_tags_ops.cpython-310.pyc,,
tests/__pycache__/test_utils_calculation.cpython-310.pyc,,
tests/__pycache__/test_utils_formatter.cpython-310.pyc,,
tests/__pycache__/test_utils_utils.cpython-310.pyc,,
tests/dummy_data/__init__.py,sha256=fXaZthNGjidAhHBwjTbB06508ebOtdBLSRMqDgwA38c,39
tests/dummy_data/__pycache__/__init__.cpython-310.pyc,,
tests/dummy_data/__pycache__/file.cpython-310.pyc,,
tests/dummy_data/__pycache__/urls.cpython-310.pyc,,
tests/dummy_data/file.py,sha256=6HXKu61M7BLfneaWk8KYaWsCQynHTe0cTGKNvv7jKuc,2136
tests/dummy_data/urls.py,sha256=fzVqhTc3-ng-FU_GxpcXiI_tIJoGhvmY5OAexTER1Oc,182
tests/helpers.py,sha256=exQ5nkSHi1NxSs_ZfRlgOx-L8gorg3-_xbdelUBu3Io,1756
tests/test_client.py,sha256=On0wZs8CkohoV8ad5AWekCNtoHQmFgd0iUN7ReVJdE8,1804
tests/test_custom_metadata_fields_ops.py,sha256=2uSZjT-s17oFthQmokY3nmPjbG7XX_akFLW5MhowJew,35697
tests/test_files_ops.py,sha256=olJbMrMONv5WWp0Ol3Sn3FqhbRocr-79zvm5JslKSjQ,556734
tests/test_folder_ops.py,sha256=NWY5wc-RBNlKANtkr-RvmPjlrdhGTjcMuFQIjF6JG8U,20838
tests/test_generate_url.py,sha256=aUS-LXcWfhzKYACUokM3f8FbJ_WdB9IyLfvIj4gURq0,19858
tests/test_models_results.py,sha256=YWhUOLt7FOWA72mdCY66GpqPY6QXudyGS7M7BfpyQdk,11201
tests/test_tags_ops.py,sha256=Qm_YrjbK32gKLuD2YT37McN6-bm8fibTbvwYIJscDvQ,12294
tests/test_utils_calculation.py,sha256=W9E1IPP7mSsn7KlmhjWr1FD3B1SqvTYv7wE-VLrfO2I,676
tests/test_utils_formatter.py,sha256=AehP4T7b5K6OJ2CjTRGJX8J8PbG8dYvItGK6xevEveY,766
tests/test_utils_utils.py,sha256=l9594HcDegKA5HVb1PQ9WLQ2LaPC3twNkefs8GxvU58,3426

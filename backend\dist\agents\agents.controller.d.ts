import { AgentsService } from './agents.service';
export declare class AgentsController {
    private readonly agentsService;
    constructor(agentsService: AgentsService);
    findAll(page?: string, limit?: string): Promise<{
        id: string;
        name: string;
        description: string;
        imageUrl: string;
        avatarUrl: string;
        views: any;
        author: string;
        tags: any;
        openingLine: string;
    }[]>;
    findOne(id: string): Promise<{
        id: string;
        name: string;
        description: string;
        imageUrl: string;
        avatarUrl: string;
        openingLine: string;
        views: any;
        fans: any;
        dialogues: any;
        starValue: any;
        tags: any;
        author: {
            id: string;
            nickname: string | null;
            avatarUrl: any;
        } | null;
        stories: {
            id: string;
            title: string;
            description: string;
            plays: any;
        }[];
    }>;
    createAgent(prompt: string): Promise<{
        id: string;
        name: string;
        description: string;
        imageUrl: string;
        avatarUrl: string;
        openingLine: string;
        tags: any;
        views: string;
        author: string | null;
    }>;
}

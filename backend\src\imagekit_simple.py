#!/usr/bin/env python3
"""
简化版ImageKit图片存储服务
使用HTTP请求直接与ImageKit API交互
"""

import os
import uuid
import asyncio
import aiohttp
import base64
import json
from typing import Optional, Dict, Any, List
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class SimpleImageKitService:
    """简化版ImageKit服务类"""
    
    def __init__(self):
        """初始化服务"""
        self.private_key = os.getenv('IMAGEKIT_PRIVATE_KEY')
        self.public_key = os.getenv('IMAGEKIT_PUBLIC_KEY')
        self.url_endpoint = os.getenv('IMAGEKIT_URL_ENDPOINT')
        
        if not all([self.private_key, self.public_key, self.url_endpoint]):
            raise ValueError("请设置所有ImageKit环境变量")
        
        # 基础API URL
        self.api_base = "https://api.imagekit.io/v1"
        
        # 认证头
        auth_string = f"{self.private_key}:"
        auth_bytes = auth_string.encode('ascii')
        auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
        self.auth_header = f"Basic {auth_b64}"
    
    async def upload_image_from_url(
        self,
        image_url: str,
        file_name: Optional[str] = None,
        folder: str = "/uploads/",
        tags: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        从URL上传图片
        
        Args:
            image_url: 图片URL
            file_name: 文件名
            folder: 文件夹
            tags: 标签列表
            
        Returns:
            上传结果
        """
        try:
            if not file_name:
                file_name = f"{uuid.uuid4().hex}.jpg"
            
            # 准备上传数据
            upload_data = {
                "file": image_url,
                "fileName": file_name,
                "folder": folder,
                "useUniqueFileName": "true"
            }
            
            if tags:
                upload_data["tags"] = ",".join(tags)
            
            # 发送请求
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.api_base}/files/upload",
                    headers={
                        "Authorization": self.auth_header
                    },
                    data=upload_data
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return {
                            "success": True,
                            "file_id": result.get("fileId"),
                            "name": result.get("name"),
                            "url": result.get("url"),
                            "file_path": result.get("filePath"),
                            "size": result.get("size"),
                            "file_type": result.get("fileType"),
                            "tags": result.get("tags", [])
                        }
                    else:
                        error_text = await response.text()
                        return {
                            "success": False,
                            "error": f"HTTP {response.status}: {error_text}"
                        }
                        
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def upload_image_from_base64(
        self,
        base64_data: str,
        file_name: Optional[str] = None,
        folder: str = "/uploads/",
        tags: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        从Base64数据上传图片
        
        Args:
            base64_data: Base64数据
            file_name: 文件名
            folder: 文件夹
            tags: 标签列表
            
        Returns:
            上传结果
        """
        try:
            if not file_name:
                file_name = f"{uuid.uuid4().hex}.jpg"
            
            # 准备上传数据
            upload_data = {
                "file": base64_data,
                "fileName": file_name,
                "folder": folder,
                "useUniqueFileName": "true"
            }
            
            if tags:
                upload_data["tags"] = ",".join(tags)
            
            # 发送请求
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.api_base}/files/upload",
                    headers={
                        "Authorization": self.auth_header
                    },
                    data=upload_data
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return {
                            "success": True,
                            "file_id": result.get("fileId"),
                            "name": result.get("name"),
                            "url": result.get("url"),
                            "file_path": result.get("filePath"),
                            "size": result.get("size"),
                            "file_type": result.get("fileType"),
                            "tags": result.get("tags", [])
                        }
                    else:
                        error_text = await response.text()
                        return {
                            "success": False,
                            "error": f"HTTP {response.status}: {error_text}"
                        }
                        
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def generate_image_url(
        self,
        file_path: str,
        width: Optional[int] = None,
        height: Optional[int] = None,
        quality: Optional[int] = None,
        format: Optional[str] = None
    ) -> str:
        """
        生成图片URL
        
        Args:
            file_path: 文件路径
            width: 宽度
            height: 高度
            quality: 质量
            format: 格式
            
        Returns:
            图片URL
        """
        try:
            url = f"{self.url_endpoint}{file_path}"
            
            # 添加变换参数
            transformations = []
            if width:
                transformations.append(f"w-{width}")
            if height:
                transformations.append(f"h-{height}")
            if quality:
                transformations.append(f"q-{quality}")
            if format:
                transformations.append(f"f-{format}")
            
            if transformations:
                url += f"?tr={','.join(transformations)}"
            
            return url
            
        except Exception as e:
            print(f"生成URL失败: {e}")
            return f"{self.url_endpoint}{file_path}"
    
    async def list_files(self, limit: int = 20) -> Dict[str, Any]:
        """
        列出文件
        
        Args:
            limit: 限制数量
            
        Returns:
            文件列表
        """
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.api_base}/files",
                    headers={
                        "Authorization": self.auth_header
                    },
                    params={"limit": limit}
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        files = []
                        
                        for file_item in result:
                            files.append({
                                "file_id": file_item.get("fileId"),
                                "name": file_item.get("name"),
                                "url": file_item.get("url"),
                                "file_path": file_item.get("filePath"),
                                "size": file_item.get("size"),
                                "file_type": file_item.get("fileType"),
                                "tags": file_item.get("tags", []),
                                "created_at": file_item.get("createdAt"),
                                "updated_at": file_item.get("updatedAt")
                            })
                        
                        return {
                            "success": True,
                            "files": files,
                            "total": len(files)
                        }
                    else:
                        error_text = await response.text()
                        return {
                            "success": False,
                            "error": f"HTTP {response.status}: {error_text}",
                            "files": [],
                            "total": 0
                        }
                        
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "files": [],
                "total": 0
            }
    
    async def delete_image(self, file_id: str) -> Dict[str, Any]:
        """
        删除图片
        
        Args:
            file_id: 文件ID
            
        Returns:
            删除结果
        """
        try:
            async with aiohttp.ClientSession() as session:
                async with session.delete(
                    f"{self.api_base}/files/{file_id}",
                    headers={
                        "Authorization": self.auth_header
                    }
                ) as response:
                    if response.status == 204:
                        return {
                            "success": True,
                            "message": f"文件 {file_id} 删除成功"
                        }
                    else:
                        error_text = await response.text()
                        return {
                            "success": False,
                            "error": f"HTTP {response.status}: {error_text}"
                        }
                        
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def upload_image(
        self,
        image_bytes: bytes,
        file_name: str,
        folder: str = "/uploads/",
        tags: Optional[List[str]] = None
    ) -> str:
        """
        上传图片字节数据到ImageKit

        Args:
            image_bytes: 图片字节数据
            file_name: 文件名
            folder: 文件夹
            tags: 标签列表

        Returns:
            图片URL，如果失败返回None
        """
        try:
            import base64
            import io

            # 将bytes转换为base64
            base64_data = base64.b64encode(image_bytes).decode('utf-8')
            base64_with_prefix = f"data:image/png;base64,{base64_data}"

            # 调用base64上传方法
            result = await self.upload_image_from_base64(
                base64_data=base64_with_prefix,
                file_name=file_name,
                folder=folder,
                tags=tags
            )

            if result["success"]:
                return result["url"]
            else:
                print(f"图片上传失败: {result['error']}")
                return None

        except Exception as e:
            print(f"图片上传异常: {e}")
            return None

    async def health_check(self) -> Dict[str, Any]:
        """
        健康检查

        Returns:
            健康状态
        """
        try:
            # 尝试列出文件
            result = await self.list_files(limit=1)

            if result["success"]:
                return {
                    "success": True,
                    "message": "ImageKit服务连接正常",
                    "url_endpoint": self.url_endpoint
                }
            else:
                return {
                    "success": False,
                    "error": result["error"],
                    "message": "ImageKit服务连接失败"
                }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "ImageKit服务连接失败"
            }


# 全局服务实例
simple_imagekit_service = SimpleImageKitService()


# 便捷函数
async def upload_image_url(url: str, **kwargs) -> Dict[str, Any]:
    """上传图片URL"""
    return await simple_imagekit_service.upload_image_from_url(url, **kwargs)


async def upload_image_base64(base64_data: str, **kwargs) -> Dict[str, Any]:
    """上传Base64图片"""
    return await simple_imagekit_service.upload_image_from_base64(base64_data, **kwargs)


def get_image_url(file_path: str, **kwargs) -> str:
    """生成图片URL"""
    return simple_imagekit_service.generate_image_url(file_path, **kwargs)


async def delete_image(file_id: str) -> Dict[str, Any]:
    """删除图片"""
    return await simple_imagekit_service.delete_image(file_id)


if __name__ == "__main__":
    # 测试代码
    async def test_service():
        print("🧪 测试简化版ImageKit服务...")
        
        # 健康检查
        health = await simple_imagekit_service.health_check()
        print(f"健康检查: {health}")
        
        # 列出文件
        files = await simple_imagekit_service.list_files(limit=5)
        print(f"文件列表: {files}")
        
        # 测试URL生成
        test_url = simple_imagekit_service.generate_image_url(
            "/test.jpg",
            width=300,
            height=200,
            quality=80,
            format="webp"
        )
        print(f"测试URL: {test_url}")
    
    asyncio.run(test_service()) 
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const common_1 = require("@nestjs/common");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    app.enableCors({
        origin: '*',
        methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
        credentials: true,
    });
    app.useGlobalPipes(new common_1.ValidationPipe());
    const port = process.env.PORT ?? 8000;
    await app.listen(port);
    console.log(`🚀 星恋 AI 后端服务启动成功！`);
    console.log(`📊 服务运行在: http://localhost:${port}`);
}
bootstrap();
//# sourceMappingURL=main.js.map
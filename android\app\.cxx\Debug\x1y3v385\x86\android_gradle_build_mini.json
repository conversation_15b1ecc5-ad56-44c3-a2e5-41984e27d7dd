{"buildFiles": ["C:\\Users\\<USER>\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\projects\\agent\\xingye\\android\\app\\.cxx\\Debug\\x1y3v385\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\projects\\agent\\xingye\\android\\app\\.cxx\\Debug\\x1y3v385\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}
#!/usr/bin/env python3
"""
星恋 AI 后端服务 - V5.0 统一消息流重构版
"""
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import asyncio
import uuid
import json
import base64
import traceback
from contextlib import asynccontextmanager
from typing import List, Dict, Any, Optional

import os
from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect, Query, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from PIL import Image
import io

from src.services.unified_llm_service import UnifiedLLMService
from src.supabase_service import supabase_service
from src.imagekit_simple import simple_imagekit_service
from src.prompt_templates import STREAMING_STORY_CHAT_PROMPT, STREAMING_ROLEPLAY_CHAT_PROMPT
from src.pydantic_models import CharacterCardImportRequest, ImportedAgentResponse, CharacterCardV2, TavernAICharacterCard
from src.prompt_assembler import prompt_assembler
from src.summarization_service import summarization_service

# 全局服务实例
llm_service = UnifiedLLMService()

# 全局并发锁字典，防止同一会话的竞态条件
chat_locks: Dict[str, asyncio.Lock] = {}

def get_chat_lock(chat_id: str) -> asyncio.Lock:
    """获取或创建指定chat_id的锁"""
    if chat_id not in chat_locks:
        chat_locks[chat_id] = asyncio.Lock()
    return chat_locks[chat_id]

async def regenerate_user_choices(chat_id: str):
    """
    重新生成用户回复选项
    """
    try:
        print(f"INFO: [WebSocket] 开始重新生成用户选项 for chat {chat_id}")

        # 获取聊天会话信息
        chat = await supabase_service.get_chat_by_id(chat_id)
        if not chat:
            print(f"ERROR: Chat {chat_id} not found")
            # 发送错误消息给前端，重置生成状态
            error_message = {
                "type": "choices_regenerated",
                "choices": [],
                "error": "聊天会话不存在"
            }
            await manager.broadcast(chat_id, json.dumps(error_message))
            return

        # 获取参与者信息
        participants = await supabase_service.get_chat_participants(chat_id)
        if not participants:
            print(f"ERROR: No participants found for chat {chat_id}")
            # 发送错误消息给前端，重置生成状态
            error_message = {
                "type": "choices_regenerated",
                "choices": [],
                "error": "未找到聊天参与者"
            }
            await manager.broadcast(chat_id, json.dumps(error_message))
            return

        # 找到第一个非主角的NPC作为目标
        # 如果是故事模式，可能有protagonist_agent_id
        story_id = chat.get('story_id')
        protagonist_agent_id = None
        if story_id:
            story_detail = await supabase_service.get_story_by_id(story_id)
            protagonist_agent_id = story_detail.get('protagonist_agent_id') if story_detail else None

        # 找到一个非主角的参与者
        agent = None
        for participant in participants:
            if participant.get('id') != protagonist_agent_id:
                agent = participant
                break

        # 如果没找到非主角，就用第一个参与者
        if not agent and participants:
            agent = participants[0]

        if not agent:
            print(f"ERROR: No agent found for chat {chat_id}")
            return

        # 获取最近的对话历史
        history_for_choices = await supabase_service.get_messages_by_chat_id(chat_id, limit=5)
        chat_history_str = "\n".join([f"{'用户' if msg['role'] == 'user' else agent.get('name', 'AI')}: {msg['content']}" for msg in history_for_choices])
        print(f"DEBUG: Chat history for regeneration: {chat_history_str[:200]}...")

        # 获取最后一条AI消息
        last_ai_message = ""
        for msg in reversed(history_for_choices):
            if msg['role'] == 'assistant':
                last_ai_message = msg['content']
                break

        if not last_ai_message:
            print(f"WARN: No AI message found for regenerating choices in chat {chat_id}")
            return

        print(f"DEBUG: Last AI message for regeneration: {last_ai_message[:100]}...")

        # 获取章节任务目标（如果是故事模式）
        chapter_task_objective = None
        if story_id:
            try:
                chapters = await supabase_service.get_story_chapters(story_id)
                if chapters:
                    # 获取当前章节（假设是第一章）
                    current_chapter = chapters[0]
                    chapter_task_objective = current_chapter.get('mission_objective_text', '')
            except Exception as e:
                print(f"WARN: Failed to get chapter task objective for regeneration: {e}")

        # 生成新的用户选项
        # 判断是否为故事模式，决定是否包含target_agent_id
        is_story_mode = story_id is not None

        # 获取参与者列表（用于序号映射）
        participants = []
        if is_story_mode:
            participants = await supabase_service.get_story_participants(story_id)

        user_choices = await llm_service.generate_user_reply_choices(
            agent_name=agent.get('name', 'AI'),
            agent_persona=agent.get('personality', ''),
            chat_history=chat_history_str,
            last_ai_message=last_ai_message,
            chapter_task_objective=chapter_task_objective,
            target_agent_id=agent.get('id'),  # 传递当前回复的agent的ID
            include_target_agent_id=is_story_mode,  # 只在故事模式下包含target_agent_id
            participants=participants  # 传递参与者列表
        )

        # 发送新选项给前端
        choices_message = {
            "type": "choices_regenerated",
            "choices": user_choices
        }
        await manager.broadcast(chat_id, json.dumps(choices_message))

        print(f"SUCCESS: [WebSocket] 成功重新生成 {len(user_choices)} 个用户选项 for chat {chat_id}")

    except Exception as e:
        print(f"ERROR: [WebSocket] 重新生成用户选项失败 for chat {chat_id}: {e}")
        traceback.print_exc()

        # 发送错误消息给前端，确保前端状态正确重置
        error_message = {
            "type": "choices_regenerated",
            "choices": [],
            "error": f"生成选项失败: {str(e)}"
        }
        try:
            await manager.broadcast(chat_id, json.dumps(error_message))
        except Exception as broadcast_error:
            print(f"ERROR: 发送错误消息失败: {broadcast_error}")

    except Exception as e:
        print(f"ERROR: Failed to regenerate user choices for chat {chat_id}: {e}")
        # 发送错误消息给前端
        error_message = {
            "type": "choices_regeneration_failed",
            "error": str(e)
        }
        await manager.broadcast(chat_id, json.dumps(error_message))

# --- Lifespan Manager ---
@asynccontextmanager
async def lifespan(app: FastAPI):
    print("INFO: 启动 星恋AI FastAPI服务器 (V5.0 统一消息流)")
    print("=" * 50)
    health = await supabase_service.health_check()
    if health["status"] != "healthy":
        print(f"FATAL: Supabase数据库连接失败: {health.get('error')}")
    else:
        print("INFO: Supabase数据库连接成功。")
    print("INFO: 所有服务启动完成")
    yield
    print("INFO: FastAPI服务器已关闭")

# --- App 初始化 ---
app = FastAPI(title="Xingye Backend - Unified Message Stream", version="5.0.0", lifespan=lifespan)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# ===================================================================
# API 端点 (V5 重构)
# ===================================================================

@app.get("/")
async def root():
    return {"message": "星恋 AI 后端服务运行中 - V5.0 统一消息流"}

@app.get("/health")
async def health_check():
    health = await supabase_service.health_check()
    return {
        "status": "healthy" if health["status"] == "healthy" else "unhealthy",
        "details": health
    }

# --- 新增：游客登录接口 (V4 - 缓存优先版) ---
class GuestLoginRequest(BaseModel):
    guest_id: str

@app.post("/api/auth/guest-login", response_model=Dict[str, Any])
async def guest_login(request: GuestLoginRequest):
    """
    健壮的游客登录接口 (V3 - 后端代理版)。
    此接口全权负责与Supabase的交互，包括创建、登录，并直接返回Session。
    前端不再直接连接Supabase进行认证。
    """
    try:
        # 1. 根据前端生成的唯一ID，构造虚拟邮箱和新的随机密码
        guest_email = f"guest_{request.guest_id}@xinglian.app"
        guest_password = str(uuid.uuid4())

        # 2. 检查用户是否存在
        users_response = await asyncio.to_thread(
            lambda: supabase_service.supabase.auth.admin.list_users()
        )
        users_list = getattr(users_response, 'users', [])
        existing_user = next((u for u in users_list if u.email == guest_email), None)

        # 3. 根据用户是否存在，执行创建或更新密码的逻辑
        if existing_user:
            print(f"INFO: 游客 '{guest_email}' 已存在。正在为其更新密码以确保可登录。")
            await asyncio.to_thread(
                lambda: supabase_service.supabase.auth.admin.update_user_by_id(
                    existing_user.id,
                    attributes={'password': guest_password}
                )
            )
        else:
            print(f"INFO: 游客 '{guest_email}' 不存在，正在为其创建新账户...")
            await asyncio.to_thread(
                lambda: supabase_service.supabase.auth.admin.create_user({
                    "email": guest_email,
                    "password": guest_password,
                    "email_confirm": True,
                    "user_metadata": { "display_name": f"游客_{request.guest_id[-6:]}" }
                })
            )
            await asyncio.sleep(1.5)
            print(f"INFO: 游客 '{guest_email}' 账户创建成功。")

        # 4. 使用普通客户端以该用户身份登录，以获取 session
        print(f"INFO: 后端正在为 '{guest_email}' 登录以获取会话...")
        session_response = await asyncio.to_thread(
            lambda: supabase_service.supabase.auth.sign_in_with_password({
                "email": guest_email,
                "password": guest_password
            })
        )
        
        # 5. 返回完整的会话信息 (通常是 JSON)
        print(f"INFO: 成功获取会话，返回给前端。")
        return session_response.model_dump()

    except Exception as e:
        print(f"ERROR: 游客登录/创建流程失败: {e}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"游客登录失败: {str(e)}")

# --- 对话创建 (入口) API ---

@app.post("/api/chats/start-with-agent/{agent_id}", response_model=Dict[str, str])
async def start_chat_with_agent(agent_id: str, user_id: str = Query(..., description="发起聊天的用户ID")):
    """
    当用户点击【角色】卡片开始聊天时调用。
    若用户已存在该角色的会话，则直接返回已有 chat_id；否则创建新的 chat。
    """
    try:
        print(f"INFO: 开始处理用户 {user_id} 与角色 {agent_id} 的会话请求")

        # 1. 检查是否已存在与该角色的会话
        existing_chat = await supabase_service.get_chat_by_user_and_agent(user_id, agent_id)
        if existing_chat:
            print(f"SUCCESS: 找到现有会话 {existing_chat['id']}，用户 {user_id} 与角色 {agent_id}")
            return {"chat_id": existing_chat["id"]}

        print(f"INFO: 未找到现有会话，为用户 {user_id} 与角色 {agent_id} 创建新会话")

        # 2. 获取agent信息，确认其存在
        agent = await supabase_service.get_agent_by_id(agent_id)
        if not agent:
            print(f"ERROR: 角色 {agent_id} 不存在")
            raise HTTPException(status_code=404, detail="Agent not found")

        # 3. 创建新会话
        chat_id = await supabase_service.create_chat_session(user_id=user_id, agent_ids=[agent_id])
        if not chat_id:
            print(f"ERROR: 为用户 {user_id} 与角色 {agent_id} 创建会话失败")
            raise HTTPException(status_code=500, detail="Failed to create chat session")

        print(f"SUCCESS: 成功创建新会话 {chat_id}，用户 {user_id} 与角色 {agent_id}")

        # 4. 添加开场白作为第一条消息
        if agent.get("first_mes"):
            await supabase_service.add_message_to_chat(
                chat_id=chat_id,
                role='assistant',
                content=agent["first_mes"],
                agent_id=agent_id
            )
            print(f"INFO: 已添加角色 {agent_id} 的开场白到会话 {chat_id}")

        return {"chat_id": chat_id}
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")

@app.post("/api/chats/start-story/{story_id}", response_model=Dict[str, str])
async def start_story_chat(story_id: str, user_id: str = Query(..., description="发起故事的用户ID")):
    """
    当用户点击【互动】卡片开始故事时调用。
    若用户已存在该故事的会话，则直接返回已有 chat_id；否则创建新的 chat，会写入开场演绎，并返回 chat_id。
    """
    try:
        existing_chat = await supabase_service.get_chat_by_user_and_story(user_id, story_id)
        if existing_chat:
            print(f"INFO: Found existing chat {existing_chat['id']} for user {user_id} and story {story_id}.")
            return {"chat_id": existing_chat["id"]}

        story = await supabase_service.get_story_by_id(story_id)
        if not story:
            raise HTTPException(status_code=404, detail="Story not found")
        
        chapters = await supabase_service.get_story_chapters(story_id)
        if not chapters:
            raise HTTPException(status_code=404, detail="Story has no chapters")
        first_chapter = chapters[0]

        # --- ▼▼▼ 核心修复开始 ▼▼▼ ---
        opening_sequence = first_chapter.get("opening_sequence") or []
        
        # 1. 从开场演绎中提取所有不重复的角色ID
        agent_ids = list(set(
            element.get("agent_id") or element.get("character_id")
            for element in opening_sequence
            if element.get("agent_id") or element.get("character_id")
        ))

        # --- 调试代码：检查提取的agent_ids ---
        print(f"--- AGENT_IDS DEBUG ---")
        print(f"Opening sequence length: {len(opening_sequence)}")
        print(f"Extracted agent_ids: {agent_ids}")
        for i, element in enumerate(opening_sequence[:3]):  # 只打印前3个元素
            agent_id = element.get('agent_id') or element.get('character_id')
            print(f"  Element {i}: type={element.get('element_type')}, agent_id={agent_id}")
        print(f"----------------------")

        # 2. 如果开场演绎中没有任何角色，可以设定一个默认或报错
        if not agent_ids:
            # 这是一个备用逻辑，理想情况下生成器应确保至少有一个角色
            # 这里我们先假设至少有一个角色，或者你可以添加一个默认角色ID
            print(f"WARN: Story {story_id} chapter 1 has no characters in opening sequence.")
            # raise HTTPException(status_code=404, detail="Story chapter has no participating agents.")
            # 暂时为了能跑通，我们还是用之前的错误逻辑作为后备
            agents = await supabase_service.get_agents(is_public=True, limit=2)
            agent_ids = [a['id'] for a in agents]

        # --- ▲▲▲ 核心修复结束 ▲▲▲ ---

        chat_id = await supabase_service.create_chat_session(user_id=user_id, agent_ids=agent_ids, story_id=story_id)
        if not chat_id:
            raise HTTPException(status_code=500, detail="Failed to create chat session for story")

        # 不再预先写入开场演绎序列，改为在 WebSocket 连接时逐条发送
        # 开场演绎序列将通过 process_opening_sequence 函数逐条处理

        return {"chat_id": chat_id}
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")


@app.post("/api/chats/{chat_id}/touch")
async def touch_chat_session(chat_id: str):
    """更新会话的updated_at时间戳，表示用户最近访问过"""
    updated = await supabase_service.touch_chat(chat_id)
    if updated:
        return {"status": "ok"}
    raise HTTPException(status_code=404, detail="Chat not found or failed to update")


# --- 数据获取 API ---

@app.get("/api/chats/{chat_id}/messages", response_model=List[Dict])
async def get_chat_messages(chat_id: str, limit: int = 50, offset: int = 0):
    """获取指定会话的历史消息"""
    messages = await supabase_service.get_messages_by_chat_id(chat_id, limit, offset)
    return messages

@app.get("/api/chats/{chat_id}/details", response_model=Dict)
async def get_chat_details(chat_id: str):
    """
    【新增】获取单个会话的完整详情。
    这是解决前端无法区分故事模式和聊天模式的关键接口。
    """
    chat_details = await supabase_service.get_chat_by_id(chat_id)
    if not chat_details:
        raise HTTPException(status_code=404, detail="Chat session not found")
    return chat_details


@app.get("/api/chats/{chat_id}/participants", response_model=List[Dict])
async def get_chat_participants(chat_id: str):
    """获取指定会话的AI参与者"""
    participants = await supabase_service.get_chat_participants(chat_id)
    return participants

@app.get("/api/user-chats", response_model=List[Dict])
async def get_user_chat_list(user_id: str, limit: int = 20):
    """获取用户的聊天会话列表"""
    chat_list = await supabase_service.get_user_chat_list(user_id, limit)
    return chat_list

# --- 新增: 排行榜 & 发现 API ---

@app.get("/api/rankings", response_model=List[Dict])
async def get_rankings(type: str = Query(..., pattern="^(story|agent)$"), period: str = Query("daily")):
    """获取排行榜 (story/agent)"""
    try:
        if type == "story":
            rankings = await supabase_service.get_story_rankings(period)
        else:
            rankings = await supabase_service.get_agent_rankings(period)
        return rankings
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to fetch rankings: {e}")


@app.get("/api/agents/public-with-creator", response_model=List[Dict])
async def get_public_agents_with_creator(limit: int = Query(10, ge=1, le=50)):
    """获取公开智能体列表 (附带创作者信息)"""
    try:
        agents = await supabase_service.get_public_agents_with_creator(limit)
        return agents
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to fetch agents: {e}")


@app.get("/api/agents/{agent_id}", response_model=Dict)
async def get_agent_detail(agent_id: str):
    """
    【新增】获取单个智能体的完整详情。
    使用Pydantic模型确保响应结构的一致性和安全性。
    """
    try:
        agent = await supabase_service.get_agent_by_id(agent_id)
        if not agent:
            raise HTTPException(status_code=404, detail="Agent not found")

        # 你可以在这里关联更多数据，比如该角色创建的故事等
        # agent['stories'] = await supabase_service.get_stories(user_id=agent['user_id'])

        # 使用Pydantic模型验证和过滤响应数据
        try:
            from .pydantic_models import AgentDetailResponse
            validated_agent = AgentDetailResponse.model_validate(agent)
            return validated_agent.model_dump()
        except Exception as validation_error:
            print(f"WARN: Agent详情数据验证失败，返回原始数据: {validation_error}")
            return agent

    except HTTPException:
        raise
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to fetch agent detail: {e}")


# --- 角色卡导入 API ---

@app.post("/api/agents/import", response_model=ImportedAgentResponse)
async def import_character_card(
    file: UploadFile = File(...),
    user_id: str = Form(...)
):
    """
    导入TavernAI角色卡 (支持PNG和JSON格式)
    """
    try:
        # 检查文件类型
        if file.content_type == 'image/png':
            # 处理PNG格式的角色卡
            file_content = await file.read()
            image = Image.open(io.BytesIO(file_content))

            # 查找 'chara' 文本块
            chara_data_b64 = image.info.get('chara')
            if not chara_data_b64:
                raise HTTPException(status_code=400, detail="PNG文件中未找到角色卡数据")

            # 解码base64数据
            try:
                json_data = json.loads(base64.b64decode(chara_data_b64))
            except Exception as e:
                raise HTTPException(status_code=400, detail=f"角色卡数据解析失败: {str(e)}")

        elif file.content_type == 'application/json':
            # 处理JSON格式的角色卡
            file_content = await file.read()
            try:
                json_data = json.loads(file_content.decode('utf-8'))
            except Exception as e:
                raise HTTPException(status_code=400, detail=f"JSON文件解析失败: {str(e)}")
        else:
            raise HTTPException(status_code=400, detail="不支持的文件格式，请上传PNG或JSON文件")

        # 确定角色卡格式和数据
        if 'spec' in json_data and 'data' in json_data:
            # v2格式
            spec = json_data.get('spec', 'chara_card_v2')
            spec_version = json_data.get('spec_version', '2.0')
            character_data = json_data['data']
        else:
            # v1格式或直接的角色数据
            spec = 'chara_card_v1'
            spec_version = '1.0'
            character_data = json_data

        # 验证必要字段
        if not character_data.get('name'):
            raise HTTPException(status_code=400, detail="角色卡缺少必要的name字段")

        # 调用数据库服务创建智能体
        new_agent = await supabase_service.create_agent_from_character_card(
            user_id=user_id,
            character_data=character_data,
            spec=spec,
            spec_version=spec_version
        )

        if not new_agent:
            raise HTTPException(status_code=500, detail="角色卡导入失败")

        return ImportedAgentResponse(
            id=str(new_agent['id']),
            name=new_agent['name'],
            message=f"角色 '{new_agent['name']}' 导入成功！"
        )

    except HTTPException:
        raise
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"导入角色卡时发生错误: {str(e)}")


@app.get("/api/stories", response_model=List[Dict])
async def get_stories(is_public: bool = Query(True), limit: int = Query(10, ge=1, le=50), user_id: Optional[str] = None):
    """获取故事列表"""
    try:
        stories = await supabase_service.get_stories(user_id=user_id, is_public=is_public, limit=limit)
        return stories
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to fetch stories: {e}")

# 新增: 故事详情 API

@app.get("/api/stories/{story_id}", response_model=Dict)
async def get_story_detail(story_id: str):
    """获取单个故事的完整详情，包括章节和角色"""
    try:
        story = await supabase_service.get_story_by_id(story_id)
        if not story:
            raise HTTPException(status_code=404, detail="Story not found")

        chapters = await supabase_service.get_story_chapters(story_id)

        # --- 核心修改：通过关联表查询该故事专属的角色 ---
        try:
            agents_response = await asyncio.to_thread(
                lambda: supabase_service.supabase.table("story_agents")
                .select("agents(*)")  # 使用外键关系直接抓取完整的agent信息
                .eq("story_id", story_id)
                .execute()
            )
            agents = [item['agents'] for item in agents_response.data] if agents_response.data else []
            # 为兼容性添加 opening_line 字段
            for agent in agents:
                if 'first_mes' in agent and 'opening_line' not in agent:
                    agent['opening_line'] = agent['first_mes']
            print(f"DEBUG: Found {len(agents)} agents for story {story_id}")
        except Exception as agents_e:
            print(f"WARN: Failed to fetch story agents, falling back to public agents: {agents_e}")
            # 如果关联表查询失败，回退到原来的逻辑
            agents = await supabase_service.get_agents(is_public=True, limit=20)

        return {
            **story,
            "chapters": chapters,
            "agents": agents,
        }
    except HTTPException:
        raise
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to fetch story detail: {e}")

# --- 公开配置 API ---

class PublicConfig(BaseModel):
    supabase_url: str
    supabase_anon_key: str

@app.get("/api/config/public", response_model=PublicConfig)
async def get_public_config():
    """
    提供给前端客户端的公开配置。
    !!! 警告：绝不能在此接口中暴露任何私有密钥 !!!
    """
    # 从已经加载的环境变量中获取值
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_anon_key = os.getenv("SUPABASE_ANON_KEY")

    if not supabase_url or not supabase_anon_key:
        raise HTTPException(status_code=500, detail="Server configuration is incomplete.")

    return {
        "supabase_url": supabase_url,
        "supabase_anon_key": supabase_anon_key,
    }

# --- 独立评分API (PRD核心要求) ---

class EvaluateRequest(BaseModel):
    user_message: str

@app.post("/api/chats/{chat_id}/evaluate", response_model=Dict[str, Any])
async def evaluate_user_message_for_story(chat_id: str, request: EvaluateRequest):
    """
    评估用户在故事模式下的输入，并返回进度增量。
    这是PRD要求的核心功能：解耦评分与聊天逻辑。
    """
    try:
        # 1. 获取当前会话和章节信息
        chat = await supabase_service.get_chat_by_id(chat_id)
        if not chat or not chat.get('story_id'):
            raise HTTPException(status_code=404, detail="Story chat session not found.")

        story_id = chat['story_id']
        chapters = await supabase_service.get_story_chapters(story_id)
        if not chapters:
            raise HTTPException(status_code=404, detail="Story has no chapters.")
        
        task_progress = chat.get('task_progress', {})

        # 【核心修复】: 更严格的章节完成状态检查
        # 首先获取当前章节ID，如果没有则默认为第一章
        current_chapter_id = task_progress.get('current_chapter_id')
        if not current_chapter_id and chapters:
            current_chapter_id = chapters[0]['id']

        # 获取当前章节的进度
        current_progress = task_progress.get('chapters', {}).get(current_chapter_id, {}).get('progress', 0)

        # 【最重要的修复】: 在函数最开始就检查章节完成状态
        if current_progress >= 100:
            return {
                "progress_increment": 0,
                "reasoning": "本章任务已完成。",
                "current_progress": 100,
                "chapter_complete": True
            }

        # 找到当前章节对象
        current_chapter = next((ch for ch in chapters if ch['id'] == current_chapter_id), chapters[0] if chapters else None)
        if not current_chapter:
            raise HTTPException(status_code=404, detail="Current chapter not found.")

        # 获取聊天历史作为上下文
        history = await supabase_service.get_messages_by_chat_id(chat_id, limit=10)

        # 2. 调用LLM服务进行评分
        score_response = await llm_service.get_story_progress_score(
            mission=current_chapter.get('mission_objective_text', ''),
            clear_condition=current_chapter.get('clear_condition_text', ''),
            current_progress=current_progress,
            history=[{"role": msg["role"], "content": msg["content"]} for msg in history],
            user_message=request.user_message
        )

        # 3. 更新数据库中的进度
        new_progress = min(current_progress + score_response.progress_increment, 100)
        new_status = "completed" if new_progress >= 100 else "in_progress"

        # 更新task_progress结构
        updated_task_progress = task_progress.copy()
        if 'chapters' not in updated_task_progress:
            updated_task_progress['chapters'] = {}
        if 'current_chapter_id' not in updated_task_progress:
            updated_task_progress['current_chapter_id'] = current_chapter['id']

        updated_task_progress['chapters'][current_chapter['id']] = {
            'progress': new_progress,
            'status': new_status
        }

        # 如果章节完成，自动解锁下一章节
        if new_progress >= 100:
            current_chapter_index = next((i for i, ch in enumerate(chapters) if ch['id'] == current_chapter['id']), -1)
            if current_chapter_index >= 0 and current_chapter_index + 1 < len(chapters):
                next_chapter = chapters[current_chapter_index + 1]
                # 解锁下一章节
                if next_chapter['id'] not in updated_task_progress['chapters']:
                    updated_task_progress['chapters'][next_chapter['id']] = {
                        'progress': 0,
                        'status': 'unlocked'
                    }
                # 更新当前章节ID为下一章节（可选，用户可以选择是否进入下一章）
                # updated_task_progress['current_chapter_id'] = next_chapter['id']

        await supabase_service.update_chat_progress(chat_id, updated_task_progress)

        # 4. 返回评分结果给前端
        return {
            "progress_increment": score_response.progress_increment,
            "current_progress": new_progress,
            "chapter_complete": new_progress >= 100
        }

    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to evaluate message: {e}")

# --- 章节推进 API ---

@app.post("/api/chats/{chat_id}/next-chapter", response_model=Dict[str, str])
async def advance_to_next_chapter(chat_id: str, user_id: str = Query(...)):
    """
    为用户进入下一章节创建一个新的聊天会话。
    返回新的 chat_id。
    """
    try:
        # 调用数据库函数来处理章节推进逻辑
        # 使用PostgreSQL函数保证原子性
        response = await asyncio.to_thread(
            lambda: supabase_service.supabase.rpc('advance_to_next_chapter', {
                'p_current_chat_id': chat_id,
                'p_user_id': user_id
            }).execute()
        )

        if response.error:
            raise Exception(response.error.message)

        new_chat_id = response.data

        if not new_chat_id:
            # 如果返回null，说明已经是最后一章了
            raise HTTPException(status_code=404, detail="已经是最后一章或找不到下一章。")

        return {"new_chat_id": new_chat_id}

    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"进入下一章失败: {str(e)}")

# --- 摘要与记忆管理 API ---

@app.post("/api/chats/{chat_id}/summary/update")
async def update_chat_summary(chat_id: str):
    """
    手动触发对话摘要更新
    """
    try:
        success = await summarization_service.update_chat_summary(chat_id)
        if success:
            return {"status": "success", "message": "摘要已更新"}
        else:
            raise HTTPException(status_code=500, detail="摘要更新失败")
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to update summary: {e}")

@app.get("/api/chats/{chat_id}/summary")
async def get_chat_summary(chat_id: str):
    """
    获取对话摘要
    """
    try:
        summary = await summarization_service.get_chat_summary(chat_id)
        return {
            "chat_id": chat_id,
            "summary": summary,
            "has_summary": summary is not None
        }
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to get summary: {e}")

@app.get("/api/chats/{chat_id}/memory-status")
async def get_memory_status(chat_id: str):
    """
    获取记忆系统状态
    """
    try:
        # 获取总消息数
        total_messages = await asyncio.to_thread(
            lambda: supabase_service.supabase.table("messages")
            .select("id", count="exact")
            .eq("chat_id", chat_id)
            .execute()
        )

        # 获取已向量化的消息数
        vectorized_messages = await asyncio.to_thread(
            lambda: supabase_service.supabase.table("messages")
            .select("id", count="exact")
            .eq("chat_id", chat_id)
            .not_.is_("embedding", "null")
            .execute()
        )

        # 获取摘要信息
        summary = await summarization_service.get_chat_summary(chat_id)

        return {
            "chat_id": chat_id,
            "total_messages": total_messages.count or 0,
            "vectorized_messages": vectorized_messages.count or 0,
            "has_summary": summary is not None,
            "memory_coverage": (vectorized_messages.count or 0) / max(total_messages.count or 1, 1) * 100
        }
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to get memory status: {e}")

# ===================================================================
# WebSocket (V5 重构)
# ===================================================================

class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, List[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, chat_id: str):
        await websocket.accept()
        if chat_id not in self.active_connections:
            self.active_connections[chat_id] = []
        self.active_connections[chat_id].append(websocket)
        print(f"INFO: WebSocket connected to chat {chat_id}. Total connections: {len(self.active_connections[chat_id])}")

    def disconnect(self, websocket: WebSocket, chat_id: str):
        if chat_id in self.active_connections:
            self.active_connections[chat_id].remove(websocket)
            if not self.active_connections[chat_id]:
                del self.active_connections[chat_id]
        print(f"INFO: WebSocket disconnected from chat {chat_id}.")

    async def broadcast(self, chat_id: str, message: str):
        if chat_id in self.active_connections:
            for connection in self.active_connections[chat_id]:
                await connection.send_text(message)

manager = ConnectionManager()

# ===================================================================
# 织梦者引擎 - 选择效果处理
# ===================================================================

async def process_choice_effect(chat_id: str, choice_effect: dict):
    """
    处理玩家选择的效果，更新游戏状态并广播变化

    Args:
        chat_id: 聊天会话ID
        choice_effect: 选择效果字典，格式如 {"角色名.好感度": "+5", "角色名.误解程度": "-10"}
    """
    try:
        print(f"INFO: 处理选择效果 for chat {chat_id}: {choice_effect}")

        # 1. 获取当前游戏状态
        chat = await supabase_service.get_chat_by_id(chat_id)
        if not chat:
            print(f"ERROR: Chat {chat_id} not found")
            return

        current_game_state = chat.get('game_state', {})
        
        # 修复：确保 current_game_state 不为 None
        if current_game_state is None:
            current_game_state = {}

        # 2. 应用选择效果
        updated_game_state = dict(current_game_state)
        state_changes = []

        for variable_name, effect_value in choice_effect.items():
            # 解析效果值（如 "+5", "-10"）
            if isinstance(effect_value, str):
                if effect_value.startswith('+'):
                    delta = int(effect_value[1:])
                elif effect_value.startswith('-'):
                    delta = -int(effect_value[1:])
                else:
                    delta = int(effect_value)
            else:
                delta = int(effect_value)

            # 更新状态值
            current_value = updated_game_state.get(variable_name, 50)  # 默认值50
            new_value = max(0, min(100, current_value + delta))  # 限制在0-100范围内
            updated_game_state[variable_name] = new_value

            # 记录变化
            state_changes.append({
                "variable": variable_name,
                "old_value": current_value,
                "new_value": new_value,
                "delta": delta
            })

            print(f"  - {variable_name}: {current_value} -> {new_value} ({delta:+d})")

        # 3. 更新数据库
        await supabase_service.update_chat_game_state(chat_id, updated_game_state)

        # 4. 生成用户友好的变化描述
        change_descriptions = []
        for change in state_changes:
            variable_parts = change["variable"].split(".")
            if len(variable_parts) >= 2:
                character_name = variable_parts[0]
                variable_type = variable_parts[1]

                if change["delta"] > 0:
                    change_descriptions.append(f"{character_name}的{variable_type}提升了！")
                elif change["delta"] < 0:
                    change_descriptions.append(f"{character_name}的{variable_type}下降了...")

        # 5. 广播状态变化
        if change_descriptions:
            await manager.broadcast(chat_id, json.dumps({
                "type": "game_state_update",
                "changes": state_changes,
                "descriptions": change_descriptions,
                "new_game_state": updated_game_state
            }))

            print(f"SUCCESS: 游戏状态更新完成 for chat {chat_id}")

    except Exception as e:
        print(f"ERROR: 处理选择效果失败 for chat {chat_id}: {e}")
        traceback.print_exc()

@app.websocket("/ws/chat/{chat_id}")
async def websocket_chat_endpoint(websocket: WebSocket, chat_id: str, user_id: str = Query(...)):
    await manager.connect(websocket, chat_id)
    try:
        # --- PRD要求: 连接建立后的状态同步逻辑 ---
        chat_session = await supabase_service.get_chat_by_id(chat_id)
        if not chat_session:
            # 如果会话不存在，立即关闭连接并告知原因
            await websocket.send_text(json.dumps({"type": "error", "content": "Chat session not found."}))
            return

        is_story_mode = chat_session.get("story_id") is not None
        protagonist_agent_id = None

        # 如果是故事模式，获取主角信息
        if is_story_mode:
            story_id = chat_session.get("story_id")
            story_detail = await supabase_service.get_story_by_id(story_id)
            if story_detail:
                protagonist_agent_id = story_detail.get("protagonist_agent_id")
                print(f"=== PROTAGONIST DEBUG (Backend) ===")
                print(f"Story ID: {story_id}")
                print(f"Story title: {story_detail.get('title')}")
                print(f"Protagonist agent ID: {protagonist_agent_id}")
                print(f"=====================================")

        # 获取NPC参与者
        participants = await supabase_service.get_chat_participants(chat_id)

        # 如果是故事模式，获取主角信息并合并
        if is_story_mode and protagonist_agent_id:
            protagonist_agent = await supabase_service.get_agent_by_id(protagonist_agent_id)
            if protagonist_agent:
                # 【修复】将主角信息也加入到参与者列表中
                participants.append(protagonist_agent)
                print(f"DEBUG: Added protagonist agent {protagonist_agent.get('name')} to participants list")

        # 构造完整的初始状态包
        initial_state_package = {
            "type": "game_state_sync",  # 消息类型：游戏状态同步
            "data": {
                "chat_id": chat_id,
                "is_story_mode": is_story_mode,
                "protagonist_agent_id": protagonist_agent_id,  # 包含主角Agent ID
                "messages": await supabase_service.get_messages_by_chat_id(chat_id, limit=100),  # 发送历史消息
                "participants": participants,  # <--- 现在这里包含了所有角色
                "task_progress": chat_session.get("task_progress", {}),
            }
        }
        await websocket.send_text(json.dumps(initial_state_package, default=str))  # 使用default=str处理datetime
        print(f"INFO: Sent initial game state for chat {chat_id}")
        
        if is_story_mode:
            print(f"INFO: Chat {chat_id} is in Story Mode.")

        while True:
            data = await websocket.receive_text()
            message_data = json.loads(data)

            # --- V5.1 新增: 处理故事模式的 "action" ---
            action = message_data.get("action")
            if is_story_mode and action == 'next':
                 # 如果是故事模式，并且客户端请求下一条，则触发开场演绎逻辑
                 # 注意：process_opening_sequence 内部已经有锁保护，这里直接调用即可
                 asyncio.create_task(process_opening_sequence(chat_id))
                 continue # 处理完action后，进入下一次循环等待

            # --- 新增: 处理重新生成选项的 "action" ---
            if action == 'regenerate_choices':
                # 重新生成用户回复选项
                asyncio.create_task(regenerate_user_choices(chat_id))
                continue # 处理完action后，进入下一次循环等待

            # --- 检查是否有 content，如果没有则跳过消息处理 ---
            content = message_data.get("content", "").strip()
            if not content:
                print(f"INFO: Received empty content message, skipping...")
                continue

            # --- 移除choice_effect处理，统一使用评分模型 ---
            # choice_effect处理已移除，所有效果由独立的评分模型处理

            # --- 原有的聊天消息处理逻辑 ---
            user_message = await supabase_service.add_message_to_chat(
                chat_id=chat_id,
                role='user',
                content=content,
                agent_id=None
            )
            if not user_message:
                await websocket.send_text(json.dumps({"type": "error", "content": "Failed to save user message."}))
                continue

            # 注释掉用户消息的回声广播，避免前端状态冲突
            # 前端已通过乐观更新显示用户消息，不需要服务器再次广播
            # await manager.broadcast(chat_id, json.dumps(user_message))

            # 异步生成用户消息的embedding
            if user_message.get('id'):
                asyncio.create_task(generate_and_save_embedding(user_message['id'], content))

            target_agent_id = message_data.get("target_agent_id")
            target_agent_index = message_data.get("target_agent_index")  # 新增：支持序号

            # 检查演绎序列状态，决定是否触发AI回复
            if is_story_mode:
                # 获取当前演绎序列状态
                chat_session = await supabase_service.get_chat_by_id(chat_id)
                task_progress = chat_session.get('task_progress', {})
                opening_sequence_index = task_progress.get('opening_sequence_index', 0)

                # 获取总序列长度
                story_id = chat_session.get('story_id')
                chapters = await supabase_service.get_story_chapters(story_id)
                opening_sequence_total = len(chapters[0].get("opening_sequence", [])) if chapters else 0

                if opening_sequence_index < opening_sequence_total:
                    # 如果演绎序列未结束，则不触发AI自由回复，而是继续发送下一个演绎元素
                    print(f"INFO: 玩家在演绎序列中做出选择，继续播放下一个元素...")
                    # 但是仍然要为这个选择进行评分
                    print(f"INFO: 为演绎过程中的选择进行评分...")
                    asyncio.create_task(process_choice_scoring_only(chat_id, user_id, content, target_agent_id))
                    asyncio.create_task(process_opening_sequence(chat_id))
                else:
                    # 如果演绎序列已结束，才触发AI的自由回复
                    print(f"INFO: 自由对话阶段，触发AI回复...")
                    asyncio.create_task(process_story_turn_with_scoring(chat_id, user_id, content, target_agent_id, target_agent_index))
            else:
                asyncio.create_task(process_ai_turn(chat_id, user_id, target_agent_id, is_story_mode))

    except WebSocketDisconnect:
        manager.disconnect(websocket, chat_id)
    except Exception as e:
        print(f"ERROR: WebSocket for chat {chat_id} error: {e}")
        traceback.print_exc()
        manager.disconnect(websocket, chat_id)


async def process_ai_turn(chat_id: str, user_id: str, target_agent_id: Optional[str], is_story_mode: bool):
    """
    处理AI的回合：获取上下文，根据模式调用LLM，保存并广播结果。
    """
    try:
        history = await supabase_service.get_messages_by_chat_id(chat_id, limit=20)
        participants = await supabase_service.get_chat_participants(chat_id)
        
        # 获取最后一条用户消息
        user_message = None
        for msg in reversed(history):
            if msg['role'] == 'user':
                user_message = msg['content']
                break
        
        if not user_message:
            raise ValueError("No user message found to respond to")
        
        # 确定回复的目标代理
        if target_agent_id:
            target_agent = next((p for p in participants if p['id'] == target_agent_id), None)
        else:
            target_agent = participants[0] if participants else None
            
        if not target_agent:
            raise ValueError("No agent available for reply")
        
        if is_story_mode:
             chat = await supabase_service.get_chat_by_id(chat_id) # 故事模式需要最新的 progress 数据
             await process_story_turn(chat, history, user_message, target_agent, participants)
        else:
            await process_regular_chat_turn(chat_id, history, user_message, target_agent)

    except Exception as e:
        print(f"ERROR: Processing AI turn for chat {chat_id} failed: {e}")
        traceback.print_exc()
        await manager.broadcast(chat_id, json.dumps({"type": "error", "content": "AI response generation failed."}))

async def process_regular_chat_turn(chat_id: str, history: list, user_message: str, agent: dict):
    """处理普通角色扮演聊天 - 优化版：立即开始AI回复，异步处理RAG"""
    try:
        # 获取会话详情以判断模式
        chat_session = await supabase_service.get_chat_by_id(chat_id)
        if not chat_session:
            raise ValueError("会话不存在")

        # 判断是故事模式还是聊天模式
        is_story_mode = chat_session.get("story_id") is not None
        mode = 'story' if is_story_mode else 'chat'

        # 使用快速提示词立即开始AI回复（不等待embedding）
        prompt = await prompt_assembler.build_fast_prompt(
            chat_id=chat_id,
            user_message=user_message,
            agent_id=agent['id'],
            mode=mode
        )
        print(f"SUCCESS: 为角色 {agent.get('name')} 构建了{mode}模式的快速提示词")
    except Exception as e:
        print(f"WARN: 快速提示词构建失败，使用降级方案: {e}")
        # 降级到原有的简单提示词
        prompt = STREAMING_ROLEPLAY_CHAT_PROMPT.format(
            agent_name=agent.get('name', 'AI'),
            agent_persona=agent.get('roleplay_prompt', ''),
            chat_history="\n".join([f"{msg['role']}: {msg['content']}" for msg in history[-5:]]),  # 只取最近5条
            user_message=user_message,
        )

    # 调用统一的流式函数
    await stream_and_save_response(chat_id, agent, prompt, is_story_mode)

    # 异步处理完整的RAG提示词（用于未来优化，不阻塞当前回复）
    asyncio.create_task(process_rag_enhancement_async(chat_id, user_message, agent['id'], mode))

async def process_rag_enhancement_async(chat_id: str, user_message: str, agent_id: str, mode: str):
    """异步处理RAG增强，不阻塞主回复流程"""
    try:
        # 在后台生成完整的RAG增强提示词
        full_prompt = await prompt_assembler.build_prompt(
            chat_id=chat_id,
            user_message=user_message,
            agent_id=agent_id,
            mode=mode
        )
        print(f"SUCCESS: 异步生成了完整的RAG增强提示词（用于未来优化）")
        # 这里可以添加其他后台处理逻辑，比如缓存、分析等
    except Exception as e:
        print(f"WARN: 异步RAG处理失败: {e}")

# --- 新增一个可复用的流式处理函数 ---
async def stream_and_save_response(chat_id: str, agent: dict, prompt: str, is_story_mode: bool):
    """调用LLM获取流式响应，广播给客户端，并最终保存到数据库。"""
    response_stream = llm_service.get_streaming_chat_response(prompt)
    full_ai_response = ""
    reply_content = ""
    temp_message_id = f"temp_{uuid.uuid4().hex}"

    # 状态变量用于解析 <thinking> 和 <reply> 标签
    in_reply_block = False
    buffer = ""

    async for chunk in response_stream:
        full_ai_response += chunk
        buffer += chunk

        while True:
            if not in_reply_block:
                start_tag_pos = buffer.find("<reply>")
                if start_tag_pos != -1:
                    in_reply_block = True
                    # 丢弃 <reply> 标签之前的所有内容
                    buffer = buffer[start_tag_pos + len("<reply>"):]
                else:
                    break # 缓冲区中没有起始标签，等待更多数据

            if in_reply_block:
                end_tag_pos = buffer.find("</reply>")
                if end_tag_pos != -1:
                    # 找到了结束标签
                    chunk_to_send = buffer[:end_tag_pos]
                    reply_content += chunk_to_send
                    await manager.broadcast(chat_id, json.dumps({
                        "type": "message_chunk",
                        "temp_id": temp_message_id,
                        "role": "assistant",
                        "agent_id": agent['id'],
                        "content_chunk": chunk_to_send
                    }))

                    # 重置状态，并处理 </reply> 之后可能存在的下一个消息块
                    in_reply_block = False
                    buffer = buffer[end_tag_pos + len("</reply>"):]
                else:
                    # 没有结束标签，发送当前缓冲区的所有内容并清空
                    chunk_to_send = buffer
                    reply_content += chunk_to_send
                    await manager.broadcast(chat_id, json.dumps({
                        "type": "message_chunk",
                        "temp_id": temp_message_id,
                        "role": "assistant",
                        "agent_id": agent['id'],
                        "content_chunk": chunk_to_send
                    }))
                    buffer = ""
                    break # 等待更多数据

        await asyncio.sleep(0.01)

    if full_ai_response.strip():
        # 如果没有使用新的标签格式，则使用完整响应作为回复内容（向后兼容）
        if not reply_content.strip():
            reply_content = full_ai_response.strip()

        # --- ▼▼▼ 新增逻辑开始 ▼▼▼ ---
        # 1. 异步生成用户回复选项
        user_choices = []
        try:
            # 获取最近的对话历史用于生成选项
            history_for_choices = await supabase_service.get_messages_by_chat_id(chat_id, limit=5)
            chat_history_str = "\n".join([f"{'用户' if msg['role'] == 'user' else agent.get('name', 'AI')}: {msg['content']}" for msg in history_for_choices])

            # 获取章节任务目标（如果是故事模式）
            chapter_task_objective = None
            if is_story_mode:
                try:
                    chat_session = await supabase_service.get_chat_by_id(chat_id)
                    story_id = chat_session.get('story_id')
                    if story_id:
                        chapters = await supabase_service.get_story_chapters(story_id)
                        if chapters:
                            # 获取当前章节（假设是第一章）
                            current_chapter = chapters[0]
                            chapter_task_objective = current_chapter.get('mission_objective_text', '')
                except Exception as e:
                    print(f"WARN: Failed to get chapter task objective: {e}")

            # 判断是否为故事模式，决定是否包含target_agent_id
            # 获取参与者列表（用于序号映射）
            participants = []
            if is_story_mode:
                participants = await supabase_service.get_story_participants(story_id)

            user_choices = await llm_service.generate_user_reply_choices(
                agent_name=agent.get('name', 'AI'),
                agent_persona=agent.get('personality', ''),
                chat_history=chat_history_str,
                last_ai_message=full_ai_response.strip(),
                chapter_task_objective=chapter_task_objective,
                target_agent_id=agent.get('id'),  # 传递当前回复的agent的ID
                include_target_agent_id=is_story_mode,  # 只在故事模式下包含target_agent_id
                participants=participants  # 传递参与者列表
            )
        except Exception as e:
            print(f"WARN: Failed to generate user choices in WebSocket: {e}")
        # --- ▲▲▲ 新增逻辑结束 ▲▲▲ ---

        # 保存时，保存包含<thinking>和<reply>的完整 full_ai_response
        ai_message = await supabase_service.add_message_to_chat(
            chat_id=chat_id, role='assistant', content=full_ai_response.strip(), agent_id=agent['id']
        )

        # --- ▼▼▼ 修改最终消息，附带选项 ▼▼▼ ---
        # 最终广播的消息，应使用解析出的、不含标签的 reply_content
        final_message_with_clean_content = ai_message.copy() if ai_message else {}
        if ai_message and reply_content.strip():
            final_message_with_clean_content['content'] = reply_content.strip()

        final_message_payload = {
            "type": "message_final",
            "temp_id": temp_message_id,
            "final_message": final_message_with_clean_content,
            "choices": user_choices # 将生成的选项附加到消息中
        }
        await manager.broadcast(chat_id, json.dumps(final_message_payload))
        # --- ▲▲▲ 修改结束 ▲▲▲ ---

        # 异步生成并保存embedding（不阻塞响应）
        if ai_message and ai_message.get('id'):
            asyncio.create_task(generate_and_save_embedding(ai_message['id'], full_ai_response))

        # 检查是否需要更新摘要（异步执行，不阻塞响应）
        asyncio.create_task(check_and_update_summary(chat_id))

# --- RAG记忆系统：异步Embedding处理 ---
async def generate_and_save_embedding(message_id: int, content: str):
    """
    异步生成并保存消息的embedding
    这个函数在后台运行，不会阻塞用户的聊天体验
    """
    try:
        print(f"INFO: 开始为消息 {message_id} 生成embedding...")

        # 生成embedding
        embedding = await llm_service.get_embedding(content)

        # 保存到数据库
        success = await supabase_service.update_message_embedding(message_id, embedding)

        if success:
            print(f"SUCCESS: 消息 {message_id} 的embedding已保存，可用于RAG检索")
        else:
            print(f"WARN: 消息 {message_id} 的embedding保存失败")

    except Exception as e:
        print(f"ERROR: 为消息 {message_id} 生成embedding失败: {e}")
        # 这里不抛出异常，因为embedding失败不应该影响正常聊天

async def check_and_update_summary(chat_id: str):
    """
    检查并更新对话摘要
    这个函数在后台运行，不会阻塞用户的聊天体验
    """
    try:
        print(f"INFO: 检查会话 {chat_id} 是否需要更新摘要...")

        # 检查是否需要更新摘要
        should_update = await summarization_service.should_update_summary(chat_id)

        if should_update:
            print(f"INFO: 开始为会话 {chat_id} 更新摘要...")
            success = await summarization_service.update_chat_summary(chat_id)

            if success:
                print(f"SUCCESS: 会话 {chat_id} 的摘要已更新")
            else:
                print(f"WARN: 会话 {chat_id} 的摘要更新失败")
        else:
            print(f"INFO: 会话 {chat_id} 暂时不需要更新摘要")

    except Exception as e:
        print(f"ERROR: 检查会话 {chat_id} 摘要更新失败: {e}")
        # 这里不抛出异常，因为摘要失败不应该影响正常聊天

# --- 新增：仅评分函数（用于演绎过程中的选择） ---
async def process_choice_scoring_only(chat_id: str, user_id: str, user_message: str, target_agent_id: Optional[str] = None):
    """
    仅对用户的选择进行评分，不触发AI回复。
    用于演绎过程中的选择评分。
    """
    try:
        # 获取聊天会话和故事信息
        chat_session = await supabase_service.get_chat_by_id(chat_id)
        if not chat_session or not chat_session.get('story_id'):
            print(f"ERROR: Chat {chat_id} is not a story chat")
            return

        story_id = chat_session['story_id']
        chapters = await supabase_service.get_story_chapters(story_id)
        if not chapters:
            print(f"ERROR: No chapters found for story {story_id}")
            return

        # 获取当前章节（假设是第一章）
        current_chapter = chapters[0]
        task_progress = chat_session.get('task_progress', {})
        current_progress = task_progress.get('current_progress', 0)

        # 获取聊天历史
        history = await supabase_service.get_messages_by_chat_id(chat_id, limit=20)

        # 延迟1秒后执行评分
        await asyncio.sleep(1.0)
        print(f"INFO: Starting choice scoring for chat {chat_id}")

        # 执行评分
        score_response = await llm_service.get_story_progress_score(
            mission=current_chapter.get('mission_objective_text', ''),
            clear_condition=current_chapter.get('clear_condition_text', ''),
            current_progress=current_progress,
            history=[{"role": msg["role"], "content": msg["content"]} for msg in history],
            user_message=user_message
        )

        if score_response:
            increment = score_response.progress_increment
            new_total = min(current_progress + increment, 100)
            is_chapter_complete = new_total >= 100

            # 更新进度
            updated_task_progress = {**task_progress, 'current_progress': new_total}
            await supabase_service.update_chat_progress(chat_id, updated_task_progress)

            # 发送评分结果 - 统一消息格式
            score_message = {
                "type": "score_update",
                "progress_increment": increment,
                "current_progress": new_total,
                "chapter_complete": is_chapter_complete
            }
            await manager.broadcast(chat_id, json.dumps(score_message))
            print(f"INFO: Choice scoring completed for chat {chat_id}: +{increment} -> {new_total}")

    except Exception as e:
        print(f"ERROR: Choice scoring failed for chat {chat_id}: {e}")
        traceback.print_exc()

# --- PRD重构后的故事回合处理 (完整版：包含实时评分) ---
async def process_story_turn_with_scoring(chat_id: str, user_id: str, user_message: str, target_agent_id: Optional[str] = None, target_agent_index: Optional[int] = None):
    """
    PRD要求：处理故事模式的完整轮次，包含实时评分反馈。
    这是游戏化体验的核心函数。

    优化：角色回复立即开始，评分延迟1秒后并行执行，提升用户体验。
    """
    try:
        # 1. 并行获取所需上下文信息
        chat_task = supabase_service.get_chat_by_id(chat_id)
        history_task = supabase_service.get_messages_by_chat_id(chat_id, limit=15)
        participants_task = supabase_service.get_chat_participants(chat_id)
        chat, history, participants = await asyncio.gather(chat_task, history_task, participants_task)

        if not chat or not chat.get('story_id'):
            raise ValueError("Not a valid story session.")

        # 2. 获取当前章节和进度
        story_id = chat['story_id']
        chapters = await supabase_service.get_story_chapters(story_id)
        if not chapters:
            raise ValueError("Story has no chapters.")

        task_progress = chat.get('task_progress', {})
        # 找出当前正在进行的章节
        current_chapter = next((ch for ch in chapters if task_progress.get('chapters', {}).get(ch['id'], {}).get('progress', 0) < 100), chapters[0])
        current_progress = task_progress.get('chapters', {}).get(current_chapter['id'], {}).get('progress', 0)

        # 3. 确定回复的AI角色（为并行执行做准备）
        # 优先使用target_agent_index，然后是target_agent_id
        if target_agent_index is not None and target_agent_index < len(participants):
            replying_agent = participants[target_agent_index]
        elif target_agent_id:
            replying_agent = next((p for p in participants if p['id'] == target_agent_id), participants[0])
        else:
            replying_agent = participants[0] if participants else None

        if not replying_agent:
            raise ValueError("No agent available for reply")

        # 4. 构建故事模式的流式回复prompt
        participant_names = [p['name'] for p in participants]
        streaming_prompt = STREAMING_STORY_CHAT_PROMPT(
            agent_name=replying_agent.get('name', 'AI'),
            agent_persona=replying_agent.get('roleplay_prompt', ''),
            mission=current_chapter.get('mission_objective_text', ''),
            present_characters=", ".join(participant_names),
            chat_history="\n".join([f"{msg['role']}: {msg['content']}" for msg in history]),
            user_message=user_message,
        )

        # 5. 【并行执行优化】立即开始AI回复，同时启动延迟评分任务
        # 立即开始AI回复流式传输
        ai_reply_task = asyncio.create_task(
            stream_and_save_response(chat_id, replying_agent, streaming_prompt, is_story_mode=True)
        )

        # 延迟1秒后开始评分任务
        scoring_task = asyncio.create_task(
            _delayed_scoring_task(
                chat_id=chat_id,
                user_message=user_message,
                current_chapter=current_chapter,
                current_progress=current_progress,
                task_progress=task_progress,
                history=history
            )
        )

        # 等待AI回复完成（评分任务在后台继续执行）
        await ai_reply_task

        print(f"INFO: AI reply completed for chat {chat_id}, scoring task running in background")

    except Exception as e:
        print(f"ERROR: process_story_turn_with_scoring failed for chat {chat_id}: {e}")
        traceback.print_exc()


async def _delayed_scoring_task(chat_id: str, user_message: str, current_chapter: dict,
                               current_progress: int, task_progress: dict, history: list):
    """
    延迟执行的评分任务，在AI回复开始1秒后执行。
    """
    try:
        # 延迟1秒
        await asyncio.sleep(1.0)

        print(f"INFO: Starting delayed scoring task for chat {chat_id}")

        # 检查当前章节是否已经完成，如果已完成则不再评分
        if current_progress >= 100:
            print(f"INFO: Chapter already completed for chat {chat_id}, skipping scoring")
            return

        # 执行评分
        score_response = await llm_service.get_story_progress_score(
            mission=current_chapter.get('mission_objective_text', ''),
            clear_condition=current_chapter.get('clear_condition_text', ''),
            current_progress=current_progress,
            history=[{"role": msg["role"], "content": msg["content"]} for msg in history],
            user_message=user_message
        )

        # 更新数据库中的进度
        new_progress = min(current_progress + score_response.progress_increment, 100)
        new_status = "completed" if new_progress >= 100 else "in_progress"

        # 更新task_progress结构
        updated_task_progress = task_progress.copy()
        if 'chapters' not in updated_task_progress:
            updated_task_progress['chapters'] = {}
        if 'current_chapter_id' not in updated_task_progress:
            updated_task_progress['current_chapter_id'] = current_chapter['id']

        updated_task_progress['chapters'][current_chapter['id']] = {
            'progress': new_progress,
            'status': new_status
        }

        # 如果章节完成，自动解锁下一章节
        if new_progress >= 100:
            # 获取章节列表来找到下一章节
            chapters = await supabase_service.get_story_chapters(current_chapter.get('story_id'))
            if chapters:
                current_chapter_index = next((i for i, ch in enumerate(chapters) if ch['id'] == current_chapter['id']), -1)
                if current_chapter_index >= 0 and current_chapter_index + 1 < len(chapters):
                    next_chapter = chapters[current_chapter_index + 1]
                    # 解锁下一章节
                    if next_chapter['id'] not in updated_task_progress['chapters']:
                        updated_task_progress['chapters'][next_chapter['id']] = {
                            'progress': 0,
                            'status': 'unlocked'
                        }

        await supabase_service.update_chat_progress(chat_id, updated_task_progress)

        # 通过WebSocket广播评分结果 - 统一消息格式
        await manager.broadcast(chat_id, json.dumps({
            "type": "score_update",
            "progress_increment": score_response.progress_increment,
            "current_progress": new_progress,
            "chapter_complete": new_progress >= 100
        }))

        print(f"INFO: Scoring completed for chat {chat_id}: +{score_response.progress_increment} -> {new_progress}")

    except Exception as e:
        print(f"ERROR: Delayed scoring task failed for chat {chat_id}: {e}")
        traceback.print_exc()

    except Exception as e:
        print(f"ERROR in process_story_turn_with_scoring for chat {chat_id}: {e}")
        traceback.print_exc()
        await manager.broadcast(chat_id, json.dumps({
            "type": "error", "content": f"处理回合时发生错误: {e}"
        }))

# --- PRD重构后的故事回合处理 (简化版) ---
async def process_story_turn(chat: dict, history: list, user_message: str, agent: dict, participants: list):
    """
    处理故事模式聊天，现在只负责生成流式回复。
    评分已解耦到独立的 /evaluate API (PRD要求)。
    """
    chat_id = chat['id']
    story_id = chat['story_id']
    
    chapters = await supabase_service.get_story_chapters(story_id)
    if not chapters: return
    
    task_progress = chat.get('task_progress', {})
    current_chapter = next((ch for ch in chapters if task_progress.get('chapters', {}).get(ch['id'], {}).get('progress', 0) < 100), chapters[0])
    
    participant_names = [p['name'] for p in participants]
    streaming_prompt = STREAMING_STORY_CHAT_PROMPT(
        agent_name=agent.get('name', 'AI'),
        agent_persona=agent.get('roleplay_prompt', ''),
        mission=current_chapter.get('mission_objective_text', ''),
        present_characters=", ".join(participant_names),
        chat_history="\n".join([f"{msg['role']}: {msg['content']}" for msg in history]),
        user_message=user_message,
    )
    
    # 直接调用流式回复函数，不再处理评分
    await stream_and_save_response(chat_id, agent, streaming_prompt, is_story_mode=True)


# --- 开场演绎序列处理函数 ---
async def process_opening_sequence(chat_id: str):
    """
    处理故事开场演绎序列的逐条发送。
    从数据库获取开场演绎序列，并逐条通过 WebSocket 发送给前端。
    使用锁防止同一会话的并发请求导致竞态条件。
    """
    # 获取会话锁，防止并发处理同一会话
    lock = get_chat_lock(chat_id)
    async with lock:
        try:
            # 1. 获取聊天会话信息
            chat = await supabase_service.get_chat_by_id(chat_id)
            if not chat or not chat.get('story_id'):
                print(f"ERROR: Chat {chat_id} is not a story chat or not found")
                return

            story_id = chat['story_id']

            # 2. 获取故事详情和主角ID
            story_detail = await supabase_service.get_story_by_id(story_id)
            protagonist_agent_id = story_detail.get('protagonist_agent_id') if story_detail else None

            # 3. 获取故事的第一章节和开场演绎序列
            chapters = await supabase_service.get_story_chapters(story_id)
            if not chapters:
                print(f"ERROR: No chapters found for story {story_id}")
                return

            first_chapter = chapters[0]
            opening_sequence = first_chapter.get("opening_sequence") or []

            if not opening_sequence:
                print(f"INFO: No opening sequence found for story {story_id}")
                return

            # 3. 获取或初始化演绎进度
            # 我们使用 chat 的 task_progress 字段来跟踪开场演绎的进度
            task_progress = chat.get('task_progress', {})
            opening_progress = task_progress.get('opening_sequence_index', 0)

            # 4. 检查是否还有未发送的演绎内容
            if opening_progress >= len(opening_sequence):
                print(f"INFO: Opening sequence already completed for chat {chat_id}")
                return

            # 5. 发送下一条演绎内容
            current_element = opening_sequence[opening_progress]
            element_type = current_element.get("element_type", "text")  # PRD使用element_type
            content = current_element.get("content_or_prompt", "")
            # 修复：使用正确的字段名 agent_id 而不是 character_id
            character_id = current_element.get("agent_id") or current_element.get("character_id")

            # --- V-FIX START: 持久化演绎内容到数据库 ---
            # 在广播前，先将这一幕作为消息存入数据库
            # 这样二次进入时，它就是历史消息的一部分了
            if content: # 只保存有内容的消息
                # 根据是否有 character_id 以及是否是主角来判断角色
                role = 'narration' # 默认为旁白
                if character_id:
                    if character_id == protagonist_agent_id:
                        role = 'user'  # <-- 关键修复：如果是主角，角色设置为 'user'
                    else:
                        role = 'assistant'

                await supabase_service.add_message_to_chat(
                    chat_id=chat_id,
                    role=role, # <-- 使用修正后的角色
                    content=content,
                    agent_id=character_id,
                    # 可以为演绎消息添加元数据，方便前端识别
                    metadata={"is_opening_sequence": True}
                )
                print(f"INFO: Saved opening sequence element to chat {chat_id} as '{role}' message (character_id: {character_id}, protagonist: {protagonist_agent_id}).")
            # --- V-FIX END ---

            # 6. 构建消息数据，包含choice选项
            message_data = {
                "type": "opening_sequence_element",
                "data": {
                    "element_type": element_type,
                    "content": content,
                    "character_id": character_id,
                    "sequence_index": opening_progress,
                    "total_elements": len(opening_sequence),
                    "is_last": opening_progress == len(opening_sequence) - 1
                }
            }

            # 如果是choice类型，添加choices数组
            if element_type == "choice" and "choices" in current_element:
                message_data["data"]["choices"] = current_element["choices"]

            # 7. 通过 WebSocket 广播消息 (这部分不变)
            await manager.broadcast(chat_id, json.dumps(message_data))

            # 8. 更新演绎进度
            new_progress = opening_progress + 1
            updated_task_progress = {**task_progress, 'opening_sequence_index': new_progress}

            # 更新数据库中的进度
            await supabase_service.update_chat_progress(chat_id, updated_task_progress)

            print(f"INFO: Sent opening sequence element {opening_progress + 1}/{len(opening_sequence)} for chat {chat_id}")

            # 9. 如果是最后一条，发送完成信号
            if new_progress >= len(opening_sequence):
                completion_message = {
                    "type": "opening_sequence_complete",
                    "data": {"chat_id": chat_id}
                }
                await manager.broadcast(chat_id, json.dumps(completion_message))
                print(f"INFO: Opening sequence completed for chat {chat_id}")

        except Exception as e:
            print(f"ERROR: Failed to process opening sequence for chat {chat_id}: {e}")
            traceback.print_exc()


if __name__ == "__main__":
    import uvicorn
    print("INFO: 启动星恋 AI 后端服务 - V5.0 统一消息流")
    print("INFO: 访问 http://127.0.0.1:8000/docs 查看API文档")
    uvicorn.run("supabase_main:app", host="0.0.0.0", port=8000, reload=True)
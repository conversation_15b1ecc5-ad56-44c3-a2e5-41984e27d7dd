#!/usr/bin/env python3
"""
星恋AI - V5.1 互动故事模板创世引擎 (带开场演绎)
一个脚本，全自动生成并填充一个完整的互动故事模板。

运行前置条件:
1. .env 文件必须已正确配置。
2. 本脚本不再依赖API服务器，可独立运行。

该脚本将:
1. 创建一个唯一的、可预测的测试用户 (<EMAIL>)。
2. 使用该用户的身份，生成一个完整的女性向恋爱互动故事模板，包括：
    - 故事背景和主题
    - 2个章节，每个章节都有明确的任务目标和通关条件
    - 3-4个专属角色
    - 每个章节包含4-5个元素的开场演绎序列
"""
import os
import sys
import asyncio
import time
import uuid
import traceback
from pathlib import Path
from typing import List, Dict, Any, Optional
import base64

from dotenv import load_dotenv
from supabase import create_client, Client

# --- 核心设置 ---
sys.path.append(str(Path(__file__).parent.parent))
load_dotenv(Path(__file__).parent.parent / '.env')

from src.services.unified_llm_service import llm_service
from src.imagekit_simple import simple_imagekit_service
from src.supabase_service import supabase_service
from src.pydantic_models import GeneratedCoreAgentData
from src.utils.user_management import create_user_if_not_exists

# --- 全局配置 ---
TEST_USER_EMAIL = "<EMAIL>"
TEST_USER_PASSWORD = "password123"
STORY_TYPE = "女性向恋爱故事"
NUM_CHAPTERS = 2
NUM_CHARACTERS = 3

# Supabase Admin 客户端 (仅用于用户创建)
supabase_url = os.getenv("SUPABASE_URL")
supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
if not supabase_url or not supabase_key:
    raise ValueError("严重错误: 缺少Supabase配置，请检查.env文件")
supabase_admin_client: Client = create_client(supabase_url, supabase_key)

# --- 核心函数 ---
# 现在使用统一的用户管理模块

async def generate_and_save_story_agent(story_theme: str, archetype: str, user_id: str) -> Optional[Dict[str, Any]]:
    """为故事生成并保存一个专属角色，返回完整的Agent字典。"""
    log_prefix = f"    [AGENT_FOR_STORY ({archetype[:10]}...)]"
    print(f"{log_prefix} 开始生成核心档案...")
    try:
        agent_data: GeneratedCoreAgentData = await llm_service.generate_core_agent_for_story(story_theme, archetype)
        
        print(f"{log_prefix} ✓ 文本数据已生成: '{agent_data.name}'")

        # 生成角色背景故事
        backstory_text = await llm_service.generate_agent_backstory(story_theme, agent_data.name, agent_data.description)
        print(f"{log_prefix}   ⎿ 生成形象图...")
        image_prompt = None
        try:
            result = await llm_service.generate_image_from_description_with_prompt(agent_data.description)
            if not result:
                print(f"{log_prefix}   ⚠ 图片生成失败，使用默认图片")
                image_url = "https://via.placeholder.com/400x400/9333ea/ffffff?text=AI+Agent"
            else:
                image_bytes, image_prompt = result
                print(f"{log_prefix}   ⎿ 上传图片...")
                upload_result = await simple_imagekit_service.upload_image_from_base64(
                    base64.b64encode(image_bytes).decode('utf-8'),
                    f"{agent_data.name.replace(' ', '_')}_{uuid.uuid4().hex[:4]}.png", "/xinglian/agent/"
                )
                if not upload_result["success"]:
                    print(f"{log_prefix}   ⚠ 图片上传失败: {upload_result['error']}，使用默认图片")
                    image_url = "https://via.placeholder.com/400x400/9333ea/ffffff?text=AI+Agent"
                else:
                    image_url = upload_result.get("url")
        except Exception as e:
            print(f"{log_prefix}   ⚠ 图片处理异常: {e}，使用默认图片")
            image_url = "https://via.placeholder.com/400x400/9333ea/ffffff?text=AI+Agent"

        print(f"{log_prefix}   ⎿ 保存到数据库...")
        new_agent = await supabase_service.create_agent(
            user_id=user_id,
            # 基础信息
            name=agent_data.name,
            description=agent_data.description,
            tags=agent_data.tags,
            image_url=image_url,
            avatar_url=image_url,
            gender=agent_data.gender,
            voice_name=agent_data.voice_name,
            # TavernAI兼容字段 (确保使用 first_mes)
            personality=agent_data.personality,
            scenario=agent_data.scenario,
            first_mes=agent_data.first_mes, # 字段已更新
            mes_example=agent_data.mes_example,
            system_prompt=agent_data.system_prompt,
            creator_notes=agent_data.creator_notes,
            # 星恋AI扩展字段
            backstory_text=backstory_text,
            # 图片生成prompt
            image_generation_prompt=image_prompt,
            # 系统字段
            is_public=True,
            is_system_agent=False,
            # 原始数据作为备份
            data=agent_data.model_dump()
        )
        if not new_agent: raise ValueError("保存智能体至数据库失败")
        
        print(f"{log_prefix} ✓ 专属角色 '{agent_data.name}' 核心档案创建成功！")
        return new_agent
    except Exception as e:
        print(f"{log_prefix} ✗ 为原型 '{archetype}' 创建角色失败: {e}")
        return None

async def generate_full_story(user_id: str):
    """根据指定类型，通过直接调用服务层生成一个完整的故事模板"""
    log_prefix = f"[STORY_GEN ({STORY_TYPE})]"
    print(f"\n{'='*60}\n{log_prefix} 开始生成...")
    
    try:
        # 1. 生成故事概念
        print(f"{log_prefix} [1/6] 正在生成故事概念...")
        story_concepts = await llm_service.generate_story_concepts(num_stories=1, story_type=STORY_TYPE)
        if not story_concepts: raise Exception("LLM未能生成故事概念")
        concept = story_concepts[0]
        story_theme = concept.get('theme_prompt')
        if not story_theme: raise ValueError("故事概念缺少'theme_prompt'")

        # 1.5 生成宏大世界观文本
        print(f"{log_prefix} [1.5/6] 正在扩展故事世界观...")
        worldview_text = await llm_service.generate_worldview_text(story_theme)

        # 2. 生成故事封面图
        print(f"{log_prefix} [2/6] 正在为故事生成封面图...")
        try:
            # 使用增强的图片生成方法
            from src.pydantic_models import ImageGenerationRequest
            import json

            image_request = ImageGenerationRequest(
                description=f"为故事《{concept.get('title', '无标题')}》设计一张引人入胜的封面图。",
                image_type="cover",
                additional_context=json.dumps({"story_theme": story_theme})
            )

            result = await llm_service.generate_enhanced_image_from_request(image_request)
            cover_image_url = None
            cover_image_prompt = None

            if result:
                cover_image_bytes, cover_image_prompt = result
                print(f"{log_prefix}   ⎿ 上传封面图...")
                upload_result = await simple_imagekit_service.upload_image_from_base64(
                    base64.b64encode(cover_image_bytes).decode('utf-8'),
                    f"story_cover_{uuid.uuid4().hex[:8]}.png",
                    "/xinglian/story/"
                )
                if upload_result["success"]:
                    cover_image_url = upload_result.get("url")
                    print(f"{log_prefix} ✓ 封面图生成并上传成功")
                else:
                    print(f"{log_prefix} ⚠ 封面图上传失败: {upload_result['error']}，使用默认封面")
                    cover_image_url = "https://via.placeholder.com/800x600/9333ea/ffffff?text=Story+Cover"
            else:
                print(f"{log_prefix} ⚠ 封面图生成失败，使用默认封面")
                cover_image_url = "https://via.placeholder.com/800x600/9333ea/ffffff?text=Story+Cover"
        except Exception as e:
            print(f"{log_prefix} ⚠ 封面图处理异常: {e}，使用默认封面")
            cover_image_url = "https://via.placeholder.com/800x600/9333ea/ffffff?text=Story+Cover"
            cover_image_prompt = None

        # 3. 创建故事记录
        print(f"{log_prefix} [3/6] 正在创建故事《{concept.get('title', '无标题')}》...")
        story = await supabase_service.create_story(
            user_id=user_id,
            title=concept.get('title', '无标题的故事'),
            theme_prompt=story_theme,
            worldview_text=worldview_text,
            cover_image_url=cover_image_url,
            cover_image_prompt=cover_image_prompt,
            is_public=True
        )
        if not story: raise Exception("创建故事记录失败")
        story_id = story['id']
        print(f"{log_prefix} ✓ 故事记录创建成功, ID: {story_id}")

        # 4. 为整个故事生成所有核心角色
        print(f"{log_prefix} [4/6] 正在为故事生成 {NUM_CHARACTERS} 个核心角色...")
        character_archetypes = [
            "一个外冷内热、身份神秘的男主角（皇帝）",
            "一个阳光开朗、始终陪伴在身边的青梅竹马",
            "一个亦正亦邪、充满魅力的竞争对手（权臣）",
            "一个温柔体贴、默默守护的年长者（太医）"
        ][:NUM_CHARACTERS]
        
        tasks = [generate_and_save_story_agent(story_theme, archetype, user_id) for archetype in character_archetypes]
        results = await asyncio.gather(*tasks)
        story_agents = [agent for agent in results if agent]
        
        if not story_agents: raise Exception("所有核心角色都创建失败，无法继续")
        
        agent_names = [agent['name'] for agent in story_agents]
        print(f"{log_prefix} ✓ 核心角色创建完毕: {', '.join(agent_names)}")

        # 5. 生成章节大纲
        print(f"{log_prefix} [5/6] 正在生成 {NUM_CHAPTERS} 个章节大纲...")
        chapters_outline = await llm_service.generate_chapter_outline(story_theme, num_chapters=NUM_CHAPTERS)
        if not chapters_outline: raise Exception("LLM未能生成章节大纲")
        
        # 6. 循环生成每个章节的详细内容和开场演绎
        print(f"{log_prefix} [6/6] 正在生成 {len(chapters_outline)} 个章节的详细内容...")
        for i, chap_info in enumerate(chapters_outline):
            chap_log_prefix = f"{log_prefix} [章节 {i+1}]"
            chap_info['chapter_number'] = i + 1
            await generate_chapter_content(story_id, story_theme, chap_info, story_agents, chap_log_prefix)
            
        print(f"\n{log_prefix} ✓ 故事《{concept.get('title', '无标题')}》模板生成完毕。\n{'='*60}")
        
    except Exception as e:
        print(f"{log_prefix} ✗ 生成故事时发生严重错误: {e}\n{traceback.format_exc()}")

async def generate_chapter_content(story_id: str, story_theme: str, chapter_info: Dict[str, Any], all_story_agents: List[Dict[str, Any]], log_prefix: str):
    """生成并保存单个章节的所有内容，处理PRD新格式的opening_sequence。"""
    print(f"{log_prefix} 开始处理...")
    try:
        # 1. 从新格式的章节信息中提取开场演绎序列
        opening_sequence = chapter_info.get('opening_sequence', [])
        
        # 2. 生成章节背景图
        print(f"{log_prefix}   ⎿ 生成章节背景图...")
        try:
            background_description = f"{story_theme} - {chapter_info.get('background_text', '')}"
            background_image_bytes = await llm_service.generate_scene_image(background_description)
            background_image_url = None

            if background_image_bytes:
                print(f"{log_prefix}   ⎿ 上传背景图...")
                upload_result = await simple_imagekit_service.upload_image_from_base64(
                    base64.b64encode(background_image_bytes).decode('utf-8'),
                    f"chapter_bg_{uuid.uuid4().hex[:8]}.png",
                    "/xinglian/story/"
                )
                if upload_result["success"]:
                    background_image_url = upload_result.get("url")
                    print(f"{log_prefix}   ✓ 背景图生成并上传成功")
                else:
                    print(f"{log_prefix}   ⚠ 背景图上传失败: {upload_result['error']}，使用默认背景")
                    background_image_url = "https://via.placeholder.com/800x600/9333ea/ffffff?text=Chapter+Background"
            else:
                print(f"{log_prefix}   ⚠ 背景图生成失败，使用默认背景")
                background_image_url = "https://via.placeholder.com/800x600/9333ea/ffffff?text=Chapter+Background"
        except Exception as e:
            print(f"{log_prefix}   ⚠ 背景图处理异常: {e}，使用默认背景")
            background_image_url = "https://via.placeholder.com/800x600/9333ea/ffffff?text=Chapter+Background"

        # 3. 替换opening_sequence中的角色ID占位符
        # 创建一个从占位符到真实agent_id的映射
        character_id_mapping = {}
        used_agents = []
        
        for element in opening_sequence:
            if element.get('character_id') and element['character_id'].startswith('CHARACTER_ID_'):
                placeholder = element['character_id']
                if placeholder not in character_id_mapping:
                    # 为这个占位符分配一个真实的agent
                    if len(used_agents) < len(all_story_agents):
                        selected_agent = all_story_agents[len(used_agents)]
                        character_id_mapping[placeholder] = selected_agent['id']
                        used_agents.append(selected_agent)
                        print(f"{log_prefix}   ⎿ 映射 {placeholder} -> {selected_agent['name']} ({selected_agent['id'][:8]}...)")
        
        # 4. 应用映射，替换占位符
        processed_opening_sequence = []
        for element in opening_sequence:
            processed_element = element.copy()
            if 'character_id' in processed_element and processed_element['character_id'] in character_id_mapping:
                processed_element['character_id'] = character_id_mapping[processed_element['character_id']]
            processed_opening_sequence.append(processed_element)
        
        print(f"{log_prefix} ✓ 开场演绎序列处理完毕，包含 {len(processed_opening_sequence)} 个元素")
        
        # 5. 创建章节记录，存入处理后的开场演绎序列
        print(f"{log_prefix}   ⎿ 保存章节到数据库...")
        await supabase_service.create_story_chapter(
            story_id=story_id,
            chapter_number=chapter_info.get('chapter_number'),
            title=chapter_info.get('title', f"第 {chapter_info.get('chapter_number')} 章"),
            mission_objective_text=chapter_info.get('mission_objective_text'),
            background_text=chapter_info.get('background_text'),
            clear_condition_text=chapter_info.get('clear_condition_text'),
            opening_sequence=processed_opening_sequence,  # <-- 存入处理后的序列
            background_image_url=background_image_url  # <-- 新增：存入背景图URL
        )
        
        print(f"{log_prefix} ✓ 内容填充完毕。")
    except Exception as e:
        print(f"{log_prefix} ✗ 处理章节时发生错误: {e}\n{traceback.format_exc()}")

async def main():
    """主执行函数"""
    start_time = time.time()
    print("="*60)
    print("🚀 [START] 启动互动故事创世引擎 (V5.1 - 带开场演绎)...")
    print("="*60)
    
    print("\n--- [STEP 1/2] 确保唯一的测试用户存在 ---")
    test_user_id = await create_user_if_not_exists(
        supabase_admin_client,
        TEST_USER_EMAIL,
        TEST_USER_PASSWORD,
        display_name="测试用户"
    )

    if not test_user_id:
        print("\n❌ [FATAL] 无法创建或获取测试用户。脚本终止。")
        return

    print("\n" + "="*60)
    print("IMPORTANT: 测试用户已就绪!")
    print(f"Email:    {TEST_USER_EMAIL}")
    print(f"Password: {TEST_USER_PASSWORD}")
    print(f"User ID:  {test_user_id}")
    print("="*60 + "\n")

    print(f"--- [STEP 2/2] 开始生成一个完整的 '{STORY_TYPE}' 故事模板 ---")
    await generate_full_story(test_user_id)

    print(f"\n[IMPORTANT] 用于前端测试的用户ID是: {test_user_id}\n")

    print("="*60)
    print(f"✅✅✅ 前端请使用此用户ID进行测试: {test_user_id} ✅✅✅")
    print("="*60)

    end_time = time.time()
    print("\n" + "="*60)
    print("✅ [COMPLETE] 互动故事创世引擎执行完毕！")
    print(f"总耗时: {end_time - start_time:.2f} 秒")
    print("数据库已填充一个新的互动故事模板。")
    print("="*60)

if __name__ == "__main__":
    print("======================================================")
    print("          星恋AI - V5.1 互动故事创世引擎")
    print("======================================================")
    print("重要提示: 本脚本将直接操作数据库，无需启动API服务器。")
    print("确保 .env 文件已正确配置了所有API密钥。")
    print("------------------------------------------------------\n")
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n[INFO] 用户手动中断了脚本。")
    except Exception as e:
        print(f"\n[FATAL] 脚本执行过程中发生未捕获的顶层异常: {e}")
        import traceback
        traceback.print_exc()
"""
统一LLM服务
整合所有服务，提供向后兼容的接口
"""

from typing import List, Dict, Any, Optional, Tuple, AsyncGenerator
from .base_llm_service import BaseLLMService
from .image_generation_service import ImageGenerationService
from .text_generation_service import TextGenerationService
from .character_service import CharacterService
from .embedding_service import EmbeddingService

from ..pydantic_models import (
    ImageGenerationRequest, GeneratedCoreAgentData, 
    StoryProgressScore, UserChoice, StructuredChatResponse
)

class UnifiedLLMService(BaseLLMService):
    """
    统一LLM服务
    整合所有专门服务，提供完整的API接口
    保持与原有LLMService的完全兼容性
    """
    
    def __init__(self):
        super().__init__()
        
        # 初始化所有专门服务
        self.image_service = ImageGenerationService()
        self.text_service = TextGenerationService()
        self.character_service = CharacterService()
        self.embedding_service = EmbeddingService()
    
    # ========================================
    # 图片生成相关方法
    # ========================================
    
    async def generate_enhanced_image_from_request(self, request: ImageGenerationRequest) -> Optional[tuple[bytes, str]]:
        """生成增强图片"""
        return await self.image_service.generate_enhanced_image_from_request(request)
    
    async def generate_structured_image_from_request(self, request: ImageGenerationRequest) -> Optional[tuple[bytes, str]]:
        """生成结构化图片"""
        return await self.image_service.generate_structured_image_from_request(request)
    
    def _convert_structured_prompt_to_string(self, structured_data: dict) -> str:
        """转换结构化提示词"""
        return self.image_service._convert_structured_prompt_to_string(structured_data)
    
    def _build_character_prompt(self, data: dict) -> str:
        """构建角色提示词"""
        return self.image_service._build_character_prompt(data)
    
    def _build_cover_prompt(self, data: dict) -> str:
        """构建封面提示词"""
        return self.image_service._build_cover_prompt(data)
    
    async def generate_image_from_description_with_prompt(self, description: str) -> Optional[Tuple[bytes, str]]:
        """根据描述生成图片"""
        return await self.character_service.generate_image_from_description_with_prompt(description)
    
    # ========================================
    # 文本生成相关方法
    # ========================================
    
    async def get_streaming_chat_response(self, prompt: str) -> AsyncGenerator[str, None]:
        """流式聊天响应"""
        async for chunk in self.text_service.get_streaming_chat_response(prompt):
            yield chunk
    
    async def generate_text_response(self, prompt: str) -> str:
        """生成文本响应"""
        return await self.text_service.generate_text_response(prompt)
    
    async def get_story_progress_score(self, mission: str, clear_condition: str, 
                                     current_progress: int, history: List[Dict[str, str]], 
                                     user_message: str) -> StoryProgressScore:
        """获取故事进度评分"""
        return await self.text_service.get_story_progress_score(
            mission, clear_condition, current_progress, history, user_message
        )
    
    async def generate_user_reply_choices(self, agent_name: str, agent_persona: str,
                                        chat_history: str, last_ai_message: str,
                                        chapter_task_objective: str = None,
                                        target_agent_id: str = None,
                                        include_target_agent_id: bool = False,
                                        participants: List[Dict] = None) -> List[UserChoice]:
        """生成用户回复选项"""
        return await self.text_service.generate_user_reply_choices(
            agent_name, agent_persona, chat_history, last_ai_message,
            chapter_task_objective, target_agent_id, include_target_agent_id, participants
        )
    

    
    async def generate_game_design_document(self, story_summary: str) -> dict:
        """生成游戏设计文档"""
        return await self.text_service.generate_game_design_document(story_summary)
    
    async def generate_worldview_text(self, story_theme: str) -> str:
        """生成世界观文本"""
        return await self.text_service.generate_worldview_text(story_theme)
    
    async def generate_agent_backstory(self, character_info: str, story_setting: str) -> str:
        """生成角色背景故事"""
        return await self.text_service.generate_agent_backstory(character_info, story_setting)
    
    async def summarize_long_novel_in_chunks(self, novel_text: str, chunk_size: int = 30000, max_concurrent: int = 5) -> str:
        """分块摘要长篇小说 - 并发处理版本"""
        return await self.text_service.summarize_long_novel_in_chunks(novel_text, chunk_size, max_concurrent)

    async def distill_story_summary(self, novel_text: str, chunk_size: int = 30000, max_concurrent: int = 5) -> str:
        """提炼故事摘要 - 支持并发处理"""
        return await self.text_service.distill_story_summary(novel_text, chunk_size, max_concurrent)

    async def generate_initial_relationship_prompt(self, character_name: str, key_variables: List[str]) -> str:
        """生成初始关系描述"""
        return await self.text_service.generate_initial_relationship_prompt(character_name, key_variables)

    async def generate_interactive_scene(self, gdd: dict, chapter_gdd: dict,
                                       novel_chunk: str, agent_map: dict,
                                       character_profiles: list = None,
                                       current_game_state: dict = None,
                                       previous_chapter_summary: str = None) -> dict:
        """生成互动场景序列"""
        return await self.text_service.generate_interactive_scene(
            gdd, chapter_gdd, novel_chunk, agent_map, character_profiles=character_profiles,
            current_game_state=current_game_state,
            previous_chapter_summary=previous_chapter_summary
        )

    async def generate_chapter_summary_for_npc(self, chapter_summary: str,
                                             key_interactions: List[str]) -> str:
        """为NPC生成章节剧本摘要"""
        return await self.text_service.generate_chapter_summary_for_npc(chapter_summary, key_interactions)
    
    # ========================================
    # 角色相关方法
    # ========================================
    
    async def generate_agent_from_analysis(self, character_gdd: Dict[str, Any],
                                         story_theme_summary: str) -> Tuple[GeneratedCoreAgentData, Optional[bytes], Optional[str]]:
        """生成角色档案"""
        return await self.character_service.generate_agent_from_analysis(
            character_gdd, story_theme_summary
        )
    
    async def create_agent_profile(self, name: str, description: str, 
                                 personality: str, scenario: str,
                                 additional_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """创建角色档案"""
        return await self.character_service.create_agent_profile(
            name, description, personality, scenario, additional_data
        )
    
    async def generate_character_variations(self, base_character: Dict[str, Any], 
                                          variation_count: int = 3) -> list[Dict[str, Any]]:
        """生成角色变体"""
        return await self.character_service.generate_character_variations(base_character, variation_count)
    
    async def analyze_character_compatibility(self, character1: Dict[str, Any], 
                                            character2: Dict[str, Any]) -> Dict[str, Any]:
        """分析角色兼容性"""
        return await self.character_service.analyze_character_compatibility(character1, character2)
    
    async def enhance_character_backstory(self, character: Dict[str, Any], 
                                        story_context: str = "") -> str:
        """增强角色背景故事"""
        return await self.character_service.enhance_character_backstory(character, story_context)
    
    # ========================================
    # 嵌入相关方法
    # ========================================
    
    async def get_embedding(self, text: str) -> List[float]:
        """获取文本嵌入向量"""
        return await self.embedding_service.get_embedding(text)
    
    async def get_embeddings_batch(self, texts: List[str]) -> List[List[float]]:
        """批量获取嵌入向量"""
        return await self.embedding_service.get_embeddings_batch(texts)
    
    def calculate_cosine_similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """计算余弦相似度"""
        return self.embedding_service.calculate_cosine_similarity(embedding1, embedding2)
    
    async def semantic_search(self, query: str, documents: List[str], 
                            top_k: int = 5) -> List[Tuple[int, str, float]]:
        """语义搜索"""
        return await self.embedding_service.semantic_search(query, documents, top_k)
    
    async def cluster_texts(self, texts: List[str], num_clusters: int = 3) -> Dict[int, List[int]]:
        """文本聚类"""
        return await self.embedding_service.cluster_texts(texts, num_clusters)
    
    async def find_duplicate_texts(self, texts: List[str], 
                                 similarity_threshold: float = 0.9) -> List[Tuple[int, int, float]]:
        """找到重复文本"""
        return await self.embedding_service.find_duplicate_texts(texts, similarity_threshold)
    
    # ========================================
    # 兼容性方法（保持原有接口）
    # ========================================
    
    # 这些方法保持与原LLMService完全相同的接口
    # 确保现有代码无需修改即可使用新架构
    
    def get_character_summary(self, character: Dict[str, Any]) -> str:
        """获取角色摘要"""
        return self.character_service.get_character_summary(character)
    
    def get_embedding_stats(self, embeddings: List[List[float]]) -> Dict[str, Any]:
        """获取嵌入统计信息"""
        return self.embedding_service.get_embedding_stats(embeddings)

# 创建全局实例，保持与原有代码的兼容性
llm_service = UnifiedLLMService()

"""
嵌入服务
专门处理向量嵌入、相似度计算等功能
"""

import asyncio
from typing import List, Dict, Any, Tuple
import numpy as np
from google import genai

from .base_llm_service import BaseLLMService, retry_on_api_error, api_manager

class EmbeddingService(BaseLLMService):
    """嵌入服务"""
    
    def __init__(self):
        super().__init__()
    
    @retry_on_api_error(max_retries=3, base_delay=5.0)
    async def get_embedding(self, text: str) -> List[float]:
        """
        获取文本的向量表示 - 已修复并发风险
        使用BaseLLMService的get_client()方法获取已配置的客户端实例
        """
        try:
            # 1. 获取已配置好API密钥的客户端实例
            client = await self.get_client()

            # 2. 使用客户端的models.embed_content方法 - 这是正确的API调用方式
            response = await asyncio.to_thread(
                client.models.embed_content,  # <--- 修正点：使用client.models.embed_content
                model=self.embedding_model_name,
                contents=text,  # 注意：参数名是contents而不是content
                config={"task_type": "RETRIEVAL_DOCUMENT"}  # 使用config参数传递任务类型
            )

            # 3. 解析响应格式 - 根据google.genai库的实际返回格式
            if hasattr(response, 'embeddings') and response.embeddings:
                # EmbedContentResponse格式：response.embeddings是一个列表
                first_embedding = response.embeddings[0]
                if hasattr(first_embedding, 'values'):
                    embedding_vector = first_embedding.values
                else:
                    embedding_vector = first_embedding
            elif hasattr(response, 'embedding'):
                # 备用格式：直接有embedding属性
                if hasattr(response.embedding, 'values'):
                    embedding_vector = response.embedding.values
                else:
                    embedding_vector = response.embedding
            elif isinstance(response, dict) and 'embeddings' in response:
                # 字典格式响应
                embedding_vector = response['embeddings'][0]['values'] if response['embeddings'] else None
            else:
                raise Exception(f"Embedding模型返回了未知格式的响应: {type(response)}")

            if not embedding_vector:
                raise Exception("Embedding模型未返回有效的向量")

            print(f"SUCCESS: [Embedding Service] 成功获取文本嵌入向量，维度: {len(embedding_vector)}")
            return embedding_vector

        except Exception as e:
            print(f"ERROR: [Embedding Service] 获取嵌入向量失败: {e}")
            raise e
    
    async def get_embeddings_batch(self, texts: List[str]) -> List[List[float]]:
        """批量获取文本嵌入向量"""
        print(f"INFO: [Embedding Service] 批量获取 {len(texts)} 个文本的嵌入向量...")
        
        embeddings = []
        for i, text in enumerate(texts):
            try:
                embedding = await self.get_embedding(text)
                embeddings.append(embedding)
                print(f"   进度: {i+1}/{len(texts)}")
            except Exception as e:
                print(f"WARN: 文本 {i+1} 嵌入失败: {e}")
                # 添加零向量作为占位符
                embeddings.append([0.0] * 768)  # 假设768维
        
        print(f"SUCCESS: [Embedding Service] 批量嵌入完成，成功: {len([e for e in embeddings if sum(e) != 0])}/{len(texts)}")
        return embeddings
    
    def calculate_cosine_similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """计算两个向量的余弦相似度"""
        try:
            # 转换为numpy数组
            vec1 = np.array(embedding1)
            vec2 = np.array(embedding2)
            
            # 计算余弦相似度
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            similarity = dot_product / (norm1 * norm2)
            return float(similarity)
            
        except Exception as e:
            print(f"ERROR: [Embedding Service] 计算相似度失败: {e}")
            return 0.0
    
    def find_most_similar(self, query_embedding: List[float], 
                         candidate_embeddings: List[List[float]], 
                         top_k: int = 5) -> List[Tuple[int, float]]:
        """找到最相似的候选项"""
        similarities = []
        
        for i, candidate_embedding in enumerate(candidate_embeddings):
            similarity = self.calculate_cosine_similarity(query_embedding, candidate_embedding)
            similarities.append((i, similarity))
        
        # 按相似度降序排序
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        return similarities[:top_k]
    
    async def semantic_search(self, query: str, documents: List[str], 
                            top_k: int = 5) -> List[Tuple[int, str, float]]:
        """语义搜索"""
        print(f"INFO: [Embedding Service] 执行语义搜索，查询: '{query[:50]}...', 文档数: {len(documents)}")
        
        # 获取查询的嵌入向量
        query_embedding = await self.get_embedding(query)
        
        # 获取所有文档的嵌入向量
        doc_embeddings = await self.get_embeddings_batch(documents)
        
        # 找到最相似的文档
        similar_indices = self.find_most_similar(query_embedding, doc_embeddings, top_k)
        
        # 构建结果
        results = []
        for idx, similarity in similar_indices:
            if idx < len(documents):
                results.append((idx, documents[idx], similarity))
        
        print(f"SUCCESS: [Embedding Service] 语义搜索完成，返回 {len(results)} 个结果")
        return results
    
    async def cluster_texts(self, texts: List[str], num_clusters: int = 3) -> Dict[int, List[int]]:
        """文本聚类"""
        print(f"INFO: [Embedding Service] 对 {len(texts)} 个文本进行聚类，目标聚类数: {num_clusters}")
        
        # 获取所有文本的嵌入向量
        embeddings = await self.get_embeddings_batch(texts)
        
        try:
            from sklearn.cluster import KMeans
            import numpy as np
            
            # 转换为numpy数组
            embedding_matrix = np.array(embeddings)
            
            # 执行K-means聚类
            kmeans = KMeans(n_clusters=num_clusters, random_state=42)
            cluster_labels = kmeans.fit_predict(embedding_matrix)
            
            # 组织结果
            clusters = {}
            for i, label in enumerate(cluster_labels):
                if label not in clusters:
                    clusters[label] = []
                clusters[label].append(i)
            
            print(f"SUCCESS: [Embedding Service] 聚类完成，聚类分布: {[(k, len(v)) for k, v in clusters.items()]}")
            return clusters
            
        except ImportError:
            print("WARN: [Embedding Service] sklearn未安装，使用简单的相似度聚类")
            return await self._simple_clustering(embeddings, num_clusters)
    
    async def _simple_clustering(self, embeddings: List[List[float]], 
                               num_clusters: int) -> Dict[int, List[int]]:
        """简单的基于相似度的聚类"""
        clusters = {}
        assigned = set()
        
        for i, embedding in enumerate(embeddings):
            if i in assigned:
                continue
            
            # 创建新聚类
            cluster_id = len(clusters)
            clusters[cluster_id] = [i]
            assigned.add(i)
            
            # 找到相似的文本
            for j, other_embedding in enumerate(embeddings):
                if j in assigned or i == j:
                    continue
                
                similarity = self.calculate_cosine_similarity(embedding, other_embedding)
                if similarity > 0.7:  # 相似度阈值
                    clusters[cluster_id].append(j)
                    assigned.add(j)
            
            # 限制聚类数量
            if len(clusters) >= num_clusters:
                break
        
        # 将剩余未分配的文本分配到最近的聚类
        for i, embedding in enumerate(embeddings):
            if i not in assigned:
                best_cluster = 0
                best_similarity = -1
                
                for cluster_id, indices in clusters.items():
                    if indices:
                        # 计算与聚类中心的相似度
                        center_embedding = embeddings[indices[0]]
                        similarity = self.calculate_cosine_similarity(embedding, center_embedding)
                        if similarity > best_similarity:
                            best_similarity = similarity
                            best_cluster = cluster_id
                
                clusters[best_cluster].append(i)
        
        return clusters
    
    async def find_duplicate_texts(self, texts: List[str], 
                                 similarity_threshold: float = 0.9) -> List[Tuple[int, int, float]]:
        """找到重复或高度相似的文本"""
        print(f"INFO: [Embedding Service] 查找重复文本，阈值: {similarity_threshold}")
        
        embeddings = await self.get_embeddings_batch(texts)
        duplicates = []
        
        for i in range(len(embeddings)):
            for j in range(i + 1, len(embeddings)):
                similarity = self.calculate_cosine_similarity(embeddings[i], embeddings[j])
                if similarity >= similarity_threshold:
                    duplicates.append((i, j, similarity))
        
        print(f"SUCCESS: [Embedding Service] 找到 {len(duplicates)} 对重复文本")
        return duplicates
    
    def get_embedding_stats(self, embeddings: List[List[float]]) -> Dict[str, Any]:
        """获取嵌入向量的统计信息"""
        if not embeddings:
            return {}
        
        try:
            embedding_matrix = np.array(embeddings)
            
            stats = {
                "count": len(embeddings),
                "dimensions": len(embeddings[0]) if embeddings else 0,
                "mean_norm": float(np.mean([np.linalg.norm(emb) for emb in embeddings])),
                "std_norm": float(np.std([np.linalg.norm(emb) for emb in embeddings])),
                "min_value": float(np.min(embedding_matrix)),
                "max_value": float(np.max(embedding_matrix)),
                "mean_value": float(np.mean(embedding_matrix))
            }
            
            return stats
            
        except Exception as e:
            print(f"ERROR: [Embedding Service] 计算统计信息失败: {e}")
            return {"error": str(e)}

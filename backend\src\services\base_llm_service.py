#!/usr/bin/env python3
"""
基础LLM服务
提供API管理、重试机制、客户端管理等通用功能
"""

import os
import asyncio
import json
import traceback
import time
from typing import Optional, Callable, Any, Type
from functools import wraps

from google import genai
from google.genai import types
from google.api_core import exceptions as google_exceptions
from dotenv import load_dotenv
import instructor  # 新增: 引入 instructor 库
from pydantic import BaseModel
from instructor import Mode  # 用于from_gemini

# --- 新增：统一的纯文本安全设置常量 ---
TEXT_ONLY_SAFETY_SETTINGS = [
    {
        "category": types.HarmCategory.HARM_CATEGORY_HARASSMENT,
        "threshold": types.HarmBlockThreshold.BLOCK_NONE,
    },
    {
        "category": types.HarmCategory.HARM_CATEGORY_HATE_SPEECH,
        "threshold": types.HarmBlockThreshold.BLOCK_NONE,
    },
    {
        "category": types.HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
        "threshold": types.HarmBlockThreshold.BLOCK_NONE,
    },
    {
        "category": types.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
        "threshold": types.HarmBlockThreshold.BLOCK_NONE,
    },
]
# ----------------------------------------

# --- 新增：统一的纯文本安全设置字典（用于Instructor-Gemini接口） ---
TEXT_ONLY_SAFETY_SETTINGS_DICT = {
    types.HarmCategory.HARM_CATEGORY_HARASSMENT: types.HarmBlockThreshold.BLOCK_NONE,
    types.HarmCategory.HARM_CATEGORY_HATE_SPEECH: types.HarmBlockThreshold.BLOCK_NONE,
    types.HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: types.HarmBlockThreshold.BLOCK_NONE,
    types.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: types.HarmBlockThreshold.BLOCK_NONE,
}
# ----------------------------------------

# 加载环境变量
load_dotenv()

class APIManager:
    """API密钥管理器"""
    
    def __init__(self):
        self.api_keys = self._load_api_keys()
        self.current_index = 0
        self.rate_limit_delay = 12  # 每个API密钥的速率限制间隔（秒）
        self.last_request_times = {}  # 记录每个API密钥的最后请求时间
        
    def _load_api_keys(self):
        """加载API密钥"""
        keys_str = os.getenv("GEMINI_API_KEYS", "")
        if not keys_str:
            raise ValueError("未找到GEMINI_API_KEYS环境变量")
        
        keys = [key.strip() for key in keys_str.split(",") if key.strip()]
        if not keys:
            raise ValueError("GEMINI_API_KEYS为空")
        
        print(f"INFO: 已加载 {len(keys)} 个Gemini API密钥，速率限制: 5次/60秒")
        return keys
    
    async def get_next_client(self):
        """获取下一个可用的API客户端"""
        for _ in range(len(self.api_keys)):
            api_key = self.api_keys[self.current_index]
            current_time = time.time()

            # 检查速率限制
            last_time = self.last_request_times.get(self.current_index, 0)
            if current_time - last_time >= self.rate_limit_delay:
                # 更新最后请求时间
                self.last_request_times[self.current_index] = current_time

                # 创建客户端
                client = genai.Client(api_key=api_key)

                print(f"INFO: 使用 API 密钥索引 {self.current_index}: ...{api_key[-4:]}")

                # 轮换到下一个密钥
                self.current_index = (self.current_index + 1) % len(self.api_keys)

                return client
            else:
                # 当前密钥还在速率限制中，尝试下一个
                self.current_index = (self.current_index + 1) % len(self.api_keys)
        
        # 所有密钥都在速率限制中，等待最短的剩余时间
        min_wait_time = min(
            self.rate_limit_delay - (current_time - self.last_request_times.get(i, 0))
            for i in range(len(self.api_keys))
        )
        
        if min_wait_time > 0:
            print(f"INFO: 所有API密钥都在速率限制中，等待 {min_wait_time:.1f} 秒...")
            await asyncio.sleep(min_wait_time)
        
        # 递归调用，获取可用客户端
        return await self.get_next_client()

# 全局API管理器实例
api_manager = APIManager()

def retry_on_api_error(max_retries: int = 3, base_delay: float = 5.0):
    """
    API错误重试装饰器
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                    
                except google_exceptions.ResourceExhausted as e:
                    last_exception = e
                    delay = base_delay * (2 ** attempt)
                    print(f"WARN: ⚠️  API配额耗尽 (尝试 {attempt + 1}/{max_retries})")
                    if attempt < max_retries - 1:
                        print(f"INFO: ⏳ 等待 {delay:.1f} 秒后切换API密钥重试...")
                        await asyncio.sleep(delay)
                    
                except google_exceptions.DeadlineExceeded as e:
                    last_exception = e
                    delay = base_delay * (2 ** attempt)
                    print(f"WARN: ⏰ API请求超时 (尝试 {attempt + 1}/{max_retries})")
                    if attempt < max_retries - 1:
                        print(f"INFO: ⏳ 等待 {delay:.1f} 秒后重试...")
                        await asyncio.sleep(delay)
                        
                except google_exceptions.ServiceUnavailable as e:
                    last_exception = e
                    delay = base_delay * (2 ** attempt)
                    print(f"WARN: 🔄 API服务过载 (尝试 {attempt + 1}/{max_retries}) - 模型繁忙中")
                    if attempt < max_retries - 1:
                        print(f"INFO: ⏳ 等待 {delay:.1f} 秒后切换API密钥重试...")
                        await asyncio.sleep(delay)
                        
                except Exception as e:
                    last_exception = e
                    delay = base_delay * (2 ** attempt)
                    print(f"ERROR: ❌ 函数 {func.__name__} 发生错误 (尝试 {attempt + 1}/{max_retries}): {str(e)[:100]}...")
                    if attempt < max_retries - 1:
                        print(f"INFO: ⏳ 等待 {delay:.1f} 秒后重试...")
                        await asyncio.sleep(delay)
            
            print(f"ERROR: [Retry Decorator] 所有重试均失败，最终错误: {last_exception}")
            raise last_exception
            
        return wrapper
    return decorator

class BaseLLMService:
    """基础LLM服务类"""
    
    def __init__(self):
        self.story_model_name = os.getenv("GEMINI_GEN_STORY_MODEL", "gemini-2.5-pro")
        self.role_model_name = os.getenv("GEMINI_GEN_ROLE_MODEL", "gemini-2.5-pro")
        self.chat_model_name = os.getenv("GEMINI_CHAT_MODEL", "gemini-2.5-flash")
        self.image_check_model_name = os.getenv("GEMINI_CHECK_IMAGE_MODEL", "gemini-2.5-flash")
        self.image_gen_model_name = os.getenv("GEMINI_GEN_IMAGE_MODEL", "gemini-2.0-flash-preview-image-generation")
        self.tts_model_name = os.getenv("GEMINI_TTS_MODEL", "gemini-2.5-flash-preview-tts")
        self.task_check_model_name = os.getenv("GEMINI_CHECK_TASK_MODEL", "gemini-2.5-flash")
        self.embedding_model_name = os.getenv("GEMINI_EMBEDDING_MODEL", "gemini-embedding-001")
        
        # 图片生成相关配置
        self.IMAGE_MAX_RETRIES = 3
        self.IMAGE_MIN_SCORE = float(os.getenv("IMAGE_MIN_SCORE", "6.0"))
        
    async def get_client(self):
        """获取API客户端"""
        return await api_manager.get_next_client()
    
    @retry_on_api_error()
    async def generate_text(self, prompt: str, model_name: Optional[str] = None,
                          temperature: float = 0.7, max_tokens: Optional[int] = None) -> str:
        """生成文本的通用方法"""
        client = await self.get_client()
        model = model_name or self.chat_model_name

        # --- 新增日志 ---
        print("="*80)
        print(f"DEBUG: [LLM Request - generate_text] Prompt sent to model: {model}")
        print(f"--- PROMPT START ---\n{prompt}\n--- PROMPT END ---")
        print("="*80)
        # --- 日志结束 ---

        config = types.GenerateContentConfig(
            temperature=temperature,
            max_output_tokens=max_tokens,
            safety_settings=TEXT_ONLY_SAFETY_SETTINGS
        )

        response = await asyncio.to_thread(
            client.models.generate_content,
            model=model,
            contents=prompt,
            config=config
        )

        if not response.text:
            raise Exception("模型未返回有效的文本响应")

        # --- 新增日志 ---
        print("="*80)
        print("DEBUG: [LLM Response - generate_text] Content received:")
        print(f"--- RESPONSE START ---\n{response.text}\n--- RESPONSE END ---")
        print("="*80)
        # --- 日志结束 ---

        print(f"SUCCESS: 成功生成文本响应，长度: {len(response.text)}")
        return response.text
    
    @retry_on_api_error()
    async def generate_json(self, prompt: str, model_name: Optional[str] = None,
                          temperature: float = 0.7) -> dict:
        """生成JSON响应的通用方法"""
        client = await self.get_client()
        model = model_name or self.role_model_name

        # --- 新增日志 ---
        print("="*80)
        print(f"DEBUG: [LLM Request - generate_json] Prompt sent to model: {model}")
        print(f"--- PROMPT START ---\n{prompt}\n--- PROMPT END ---")
        print("="*80)
        # --- 日志结束 ---

        response = await asyncio.to_thread(
            client.models.generate_content,
            model=model,
            contents=prompt,
            config=types.GenerateContentConfig(
                response_mime_type="application/json",
                temperature=temperature,
                safety_settings=TEXT_ONLY_SAFETY_SETTINGS
            )
        )

        if not response.text:
            raise Exception("模型未返回有效的JSON响应")

        # --- 新增日志 ---
        print("="*80)
        print("DEBUG: [LLM Response - generate_json] Raw JSON received:")
        print(f"--- RESPONSE START ---\n{response.text}\n--- RESPONSE END ---")
        print("="*80)
        # --- 日志结束 ---

        try:
            json_data = json.loads(response.text)
            print(f"SUCCESS: 成功生成JSON响应，包含 {len(json_data)} 个字段")
            return json_data
        except json.JSONDecodeError as e:
            print(f"ERROR: JSON解析失败: {e}")
            print(f"DEBUG: 原始响应: {response.text}")
            raise ValueError(f"JSON解析失败: {e}")
    
    @retry_on_api_error()
    async def generate_structured_response(
         self,
         prompt: Any,
         response_model: Type[BaseModel], # BaseModel from pydantic
         model_name: Optional[str] = None,
         temperature: float = 0.7,
     ) -> Any:
        """
        使用 Gemini 原生 JSON 响应能力 + Pydantic 校验，返回结构化数据。
        """
        print(
            f"INFO: [Gemini] 正在为模型 '{model_name or self.story_model_name}' 生成 {response_model.__name__} 结构化响应…"
        )

        try:
            # 1. 直接调用 generate_json 生成原始 JSON 数据
            raw_json: dict = await self.generate_json(
                prompt=prompt,
                model_name=model_name or self.story_model_name,
                temperature=temperature,
            )

            # 2. 使用 Pydantic 进行结构化验证
            # 某些情况下模型会返回 List[dict]，而不是 dict
            if isinstance(raw_json, list):
                if len(raw_json) == 0:
                    raise ValueError("LLM 返回了空列表，无法解析为结构化对象")
                raw_json_to_validate = raw_json[0]
            else:
                raw_json_to_validate = raw_json

            structured_obj = response_model.model_validate(raw_json_to_validate)

            print(
                f"SUCCESS: [Gemini] {response_model.__name__} 对象生成并验证成功。"
            )
            return structured_obj

        except Exception as e:
            print(f"ERROR: [Gemini] 生成结构化响应失败: {e}")
            traceback.print_exc()
            raise e
    
    def log_success(self, operation: str, details: str = ""):
        """记录成功日志"""
        print(f"SUCCESS: {operation}" + (f" - {details}" if details else ""))
    
    def log_error(self, operation: str, error: Exception):
        """记录错误日志"""
        print(f"ERROR: {operation} - {error}")
        traceback.print_exc()
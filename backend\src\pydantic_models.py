from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Literal
import uuid

# 在原生JSON模式下，不再需要ConfigDict

# --- v 核心修改 v ---
# 1. 将 GeneratedAgentData 重命名为 GeneratedCoreAgentData，以反映其新用途
# 2. 移除 opening_line 字段
# 3. 新增 long_term_goal 和 relationship_with_protagonist 字段
class GeneratedCoreAgentData(BaseModel):
    """由LLM为数据库生成的【核心】智能体档案数据，完全兼容TavernAI格式"""
    # === 基础信息 ===
    name: str = Field(description="智能体的名字")
    gender: str = Field(description="角色的性别，从 ['male', 'female', 'other'] 中选择一个")
    description: str = Field(description="一段100-150字的生动角色描述，用于卡片展示和核心档案")
    tags: List[str] = Field(description="5-7个精准的性格标签")
    voice_name: str = Field(description="从提供的列表中选择一个最适合角色性格的音色名称")

    # === TavernAI兼容核心字段 ===
    personality: str = Field(description="角色的核心性格特征，详细描述性格、行为模式、说话风格等")
    scenario: str = Field(description="角色所处的场景背景，描述环境、关系、当前状况等")
    first_mes: str = Field(description="角色的开场白，第一次见面时说的话")
    mes_example: str = Field(description="对话示例，展示角色的说话风格和互动方式，使用{{char}}和{{user}}占位符")
    system_prompt: str = Field(description="系统级提示词，用于指导AI的角色扮演行为")
    creator_notes: str = Field(description="创作者备注，包含角色的创作思路和使用建议")

    # === 星恋AI扩展字段 ===
    roleplay_prompt: str = Field(description="完整的、用于角色扮演的核心指令")
    long_term_goal: str = Field(description="该角色在整个故事中的长期目标或潜在动机")
    relationship_with_protagonist: str = Field(description="该角色与玩家（主角）的根本关系，例如：宿敌、守护者、利用与被利用者等")

# --- v FIX START v ---
class ResponseMessagePart(BaseModel):
    """单个响应片段的模型，用于结构化输出"""
    action: str = Field(description="角色的动作或心理活动，必须用括号包裹，例如：(她歪了歪头)")
    dialogue: str = Field(description="角色说的具体台词，不包含任何引号")

class StructuredChatResponse(BaseModel):
    """结构化的聊天响应，包含一个或多个部分"""
    parts: List[ResponseMessagePart] = Field(description="一个包含对话和动作部分的列表")
# --- ^ FIX END ^ ---


class TTSRequest(BaseModel):
    """文本转语音的请求体"""
    text: str
    voice_name: str = Field(default="Kore", description="参考Google TTS支持的语音名称")

class AgentCreateRequest(BaseModel):
    """创建智能体的请求体"""
    prompt: str = Field(description="用户输入的智能体创建提示")
    user_id: str = Field(description="创建智能体的用户ID")

class AgentGenerateRequest(BaseModel):
    """一键生成智能体的请求体"""
    prompt: str = Field(description="用户输入的智能体创建提示")
    user_id: uuid.UUID = Field(description="创建智能体的用户ID")

class GuestLoginRequest(BaseModel):
    """游客登录请求体"""
    device_id: str = Field(description="设备的唯一标识符")

class ChatMessage(BaseModel):
    """聊天消息模型"""
    role: str = Field(description="消息角色：user 或 assistant")
    content: str = Field(description="消息内容")

# --- 新增：互动故事相关模型 ---

class StructuredImagePrompt(BaseModel):
    """结构化图片提示词模型"""
    # 基础信息
    image_type: str = Field(description="图片类型：character, cover, background, plot")
    gender: Optional[str] = Field(None, description="性别：male, female")
    character_count: Optional[int] = Field(1, description="角色数量：1 或 2")

    # 外貌特征
    age_description: Optional[str] = Field(None, description="年龄描述")
    hair_style: Optional[str] = Field(None, description="发型")
    hair_color: Optional[str] = Field(None, description="发色")
    eye_color: Optional[str] = Field(None, description="眼色")
    eye_expression: Optional[str] = Field(None, description="眼神表情")
    eye_adjective: Optional[str] = Field(None, description="眼部形容词")

    # 职业和表情
    profession: Optional[str] = Field(None, description="职业")
    facial_expression: Optional[str] = Field(None, description="面部表情")
    gaze_direction: Optional[str] = Field(None, description="视线方向")
    posture: Optional[str] = Field(None, description="姿势")

    # 服装和背景
    clothing: Optional[str] = Field(None, description="服装描述")
    background: Optional[str] = Field(None, description="背景描述")

    # 特殊元素（用于故事封面的双人互动）
    interaction: Optional[str] = Field(None, description="角色间互动描述")
    special_elements: Optional[List[str]] = Field(None, description="特殊元素列表")

class ImageGenerationRequest(BaseModel):
    """图片生成请求模型"""
    description: str = Field(description="图片描述")
    image_type: str = Field(description="图片类型：character, cover, background, plot")
    additional_context: Optional[str] = Field(None, description="额外上下文信息")

class StoryChapter(BaseModel):
    """互动故事的章节模型"""
    chapter_number: int = Field(description="章节序号")
    title: str = Field(description="章节标题")
    thumbnail_url: Optional[str] = Field(None, description="章节缩略图URL")
    # 注意：status 将在服务层动态计算，此处不定义

class InteractiveStoryDetail(BaseModel):
    """互动故事详情页的完整数据模型"""
    id: str = Field(description="故事ID")
    title: str = Field(description="故事标题")
    cover_url: Optional[str] = Field(None, description="封面图URL")
    cover_image_prompt: Optional[str] = Field(None, description="封面图生成提示词")
    full_description: str = Field(description="完整的故事描述")
    author: str = Field(description="作者名")
    last_update: str = Field(description="最后更新日期")
    chapters: List[StoryChapter] = Field(description="章节列表")

# --- 新增：用于故事生成的Pydantic模型 ---

class StoryNodeContinuation(BaseModel):
    """由LLM生成的单个剧情节点的数据结构"""
    type: str = Field(description="节点类型: 'narration', 'dialogue', 或 'choice'")
    content: str = Field(description="节点内容 (文本或选择题干)")
    characterName: Optional[str] = Field(None, description="对话角色名 (仅限dialogue类型)")
    choices: Optional[List[str]] = Field(None, description="选项列表 (仅限choice类型)")
    image_url: Optional[str] = Field(None, description="与此节点关联的图片URL (可选)")

class StoryContinuationResponse(BaseModel):
    """LLM返回的完整剧情续写响应"""
    nodes: List[StoryNodeContinuation]

# --- 新增: 用于故事进度评分的Pydantic模型 ---
class StoryProgressScore(BaseModel):
    """LLM在故事模式下返回的、只包含进度评估的聊天响应"""
    progress_increment: int = Field(description="根据用户输入对完成任务的贡献度，给出的进度增量（15-30之间）", ge=15, le=30)
    is_mission_complete: bool = Field(description="任务是否已完成")

# ========================================
# 互动场景生成相关模型
# ========================================

class ChoiceElement(BaseModel):
    """选择元素中的单个选项"""
    text: str = Field(description="选项的显示文本")
    target_agent_id: Optional[str] = Field(default=None, description="选项针对的角色ID")
    target_agent_index: Optional[int] = Field(default=None, description="选项针对的角色索引 (e.g., 0, 1, 2)")

class SceneElement(BaseModel):
    """互动场景中的单个元素"""
    element_type: Literal['narration', 'dialogue', 'choice', 'image'] = Field(description="元素类型")
    content_or_prompt: str = Field(description="元素的内容或提示")
    agent_id: Optional[str] = Field(default=None, description="对话元素的角色ID")
    agent_index: Optional[int] = Field(default=None, description="对话元素的角色索引 (e.g., 0, 1, 2)")
    choices: Optional[List[ChoiceElement]] = Field(default=None, description="选择元素的选项列表")

class InteractiveSceneResponse(BaseModel):
    """LLM返回的完整互动场景响应"""
    interactive_sequence: List[SceneElement] = Field(description="互动场景序列")
    completion_summary: str = Field(description="章节完成总结")

# ========================================
# 游戏设计文档(GDD)相关模型
# ========================================

class GDDCharacter(BaseModel):
    """GDD中的角色定义"""
    name: str = Field(description="角色名称")
    role: str = Field(description="角色在故事中的作用")
    personality_core: str = Field(description="角色的核心性格特征")
    emotional_appeal: str = Field(description="角色的情感吸引力")
    is_protagonist: bool = Field(description="是否为主角")
    key_scenes_description: str = Field(description="角色关键场景描述")

class GDDChapter(BaseModel):
    """GDD中的章节定义"""
    chapter_number: int = Field(description="章节编号", ge=1)
    title: str = Field(description="章节标题")
    summary: str = Field(description="章节摘要")
    mission_objective_text: str = Field(description="任务目标描述")
    clear_condition_text: str = Field(description="通关条件描述")
    emotional_goal: str = Field(description="情感目标")
    emotional_peak: str = Field(description="情感高潮点")
    key_interactions: List[str] = Field(description="关键互动列表")

class GameDesignDocument(BaseModel):
    """完整的游戏设计文档"""
    title: str = Field(description="故事标题")
    theme_summary: str = Field(description="主题摘要")
    worldview_text: str = Field(description="世界观设定")
    characters: List[GDDCharacter] = Field(description="角色列表")
    chapters: List[GDDChapter] = Field(description="章节列表")

# ========================================
# API响应模型
# ========================================

class ChatParticipantInfo(BaseModel):
    """聊天参与者信息"""
    id: str = Field(description="参与者ID")
    name: str = Field(description="参与者名称")
    avatar_url: Optional[str] = Field(default=None, description="头像URL")

class ChatListItemResponse(BaseModel):
    """聊天列表项响应"""
    chat_id: str = Field(description="聊天ID")
    story_id: Optional[str] = Field(default=None, description="故事ID")
    participants: Optional[List[ChatParticipantInfo]] = Field(default=None, description="参与者列表")
    latest_message: Optional[str] = Field(default=None, description="最新消息")
    latest_message_time: Optional[str] = Field(default=None, description="最新消息时间")
    updated_at: str = Field(description="更新时间")
    is_story: bool = Field(description="是否为故事模式")
    display_name: str = Field(description="显示名称")
    display_avatar: Optional[str] = Field(default=None, description="显示头像")

class AgentDetailResponse(BaseModel):
    """智能体详情响应"""
    id: str = Field(description="智能体ID")
    name: str = Field(description="智能体名称")
    description: Optional[str] = Field(default=None, description="描述")
    image_url: Optional[str] = Field(default=None, description="图片URL")
    avatar_url: Optional[str] = Field(default=None, description="头像URL")
    tags: List[str] = Field(default=[], description="标签列表")
    dialogue_count: int = Field(default=0, description="对话数量")
    creator_name: Optional[str] = Field(default="匿名创作者", description="创作者名称")

class UserChoice(BaseModel):
    """用户回复选项模型"""
    text: str = Field(description="选项的文本内容")
    target_agent_id: Optional[str] = Field(description="目标角色ID，如果未指定则使用默认逻辑", default=None)
    target_agent_index: Optional[int] = Field(description="目标角色序号（1, 2, 3...），优先于target_agent_id", default=None)

class UserChoicesResponse(BaseModel):
    """用户回复选项响应模型"""
    choices: List[UserChoice] = Field(description="用户回复选项列表")

# --- 修改: StoryChatResponseWithProgress 不再需要，可以删除或注释掉 ---
# class StoryChatResponseWithProgress(BaseModel):
#     """LLM在故事模式下返回的带进度评估的聊天响应"""
#     reply: str = Field(description="AI角色扮演的回复内容，可以包含动作和对话。")
#     progress_increment: int = Field(description="根据用户输入对完成任务的贡献度，给出的进度增量（0-100之间）", ge=0, le=100)
#     reasoning: str = Field(description="模型给出该进度增量的简要理由。")

# --- 新增：角色卡导入相关模型 ---

class CharacterCardImportRequest(BaseModel):
    """角色卡导入请求体"""
    user_id: str = Field(description="导入角色卡的用户ID")

class TavernAICharacterCard(BaseModel):
    """TavernAI角色卡数据结构 (兼容v1和v2)"""
    name: str = Field(description="角色名称")
    description: Optional[str] = Field(None, description="角色描述")
    personality: Optional[str] = Field(None, description="性格特征")
    scenario: Optional[str] = Field(None, description="场景设定")
    first_mes: Optional[str] = Field(None, description="第一条消息")
    mes_example: Optional[str] = Field(None, description="对话示例")
    creator_notes: Optional[str] = Field(None, description="创作者备注")
    system_prompt: Optional[str] = Field(None, description="系统提示")
    post_history_instructions: Optional[str] = Field(None, description="历史后指令")
    alternate_greetings: Optional[List[str]] = Field(None, description="备选问候语")
    character_book: Optional[Dict[str, Any]] = Field(None, description="角色书/世界书")
    tags: Optional[List[str]] = Field(None, description="标签")
    creator: Optional[str] = Field(None, description="创作者")
    character_version: Optional[str] = Field(None, description="角色版本")

class CharacterCardV2(BaseModel):
    """TavernAI角色卡v2格式"""
    spec: str = Field(description="规格版本")
    spec_version: str = Field(description="规格版本号")
    data: TavernAICharacterCard = Field(description="角色数据")

class ImportedAgentResponse(BaseModel):
    """导入角色卡后的响应"""
    id: str = Field(description="新创建的智能体ID")
    name: str = Field(description="角色名称")
    message: str = Field(description="导入结果消息")

# --- 新增：图片评估模型 ---
class ImageEvaluation(BaseModel):
    """图片质量评估结果，由视觉模型返回"""
    score: float = Field(description="图片综合评分（0.0 - 10.0）", ge=0.0, le=10.0)
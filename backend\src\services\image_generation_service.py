"""
图片生成服务
专门处理图片生成、优化、审核等功能
"""

import asyncio
import json
from typing import Optional, <PERSON>ple
from PIL import Image
import io

from google.genai import types
from .base_llm_service import BaseLLMService, retry_on_api_error
from ..pydantic_models import ImageGenerationRequest
from ..pydantic_models import ImageEvaluation
from ..prompt_templates import (
    CHARACTER_IMAGE_PROMPT_OPTIMIZER,
    STORY_COVER_PROMPT_OPTIMIZER,
    CHARACTER_IMAGE_EVALUATION_PROMPT,
    STORY_COVER_EVALUATION_PROMPT,
    extract_female_preference_keywords
)
from ..image_generation_options import validate_character_options, validate_cover_options

# 删除本地 ImageEvaluation 定义，使用 Pydantic 版本

class ImageGenerationService(BaseLLMService):
    """图片生成服务"""
    
    def __init__(self):
        super().__init__()
        
        # 图片类型对应的优化器和评估器
        self.optimizers = {
            "character": CHARACTER_IMAGE_PROMPT_OPTIMIZER,
            "cover": STORY_COVER_PROMPT_OPTIMIZER
        }

        self.evaluators = {
            "character": CHARACTER_IMAGE_EVALUATION_PROMPT,
            "cover": STORY_COVER_EVALUATION_PROMPT
        }
    
    @retry_on_api_error()
    async def generate_structured_image_from_request(self, request: ImageGenerationRequest) -> Optional[tuple[bytes, str]]:
        """使用结构化JSON方式生成图片"""
        print(f"INFO: 正在使用结构化方式生成 {request.image_type} 图片...")
        
        # 提取女性偏好关键词
        female_keywords = extract_female_preference_keywords(request.description)
        
        # 选择合适的结构化模板
        if request.image_type == "character":
            template = self._get_structured_character_image_prompt(request.description)
        elif request.image_type == "cover":
            # 从additional_context中提取故事简介
            additional_context = json.loads(request.additional_context or "{}")
            story_synopsis = additional_context.get("story_synopsis", request.description)
            template = self._get_structured_story_cover_prompt(story_synopsis)
        else:
            # 对于background和plot类型，暂时不支持结构化生成
            raise ValueError(f"结构化生成暂不支持图片类型: {request.image_type}")
        
        # 生成结构化JSON
        structured_data = await self.generate_json(template, self.role_model_name)
        print(f"   [+] 生成的结构化数据: {json.dumps(structured_data, ensure_ascii=False, indent=2)}")
        
        # 验证选项是否都在预定义列表中
        if request.image_type == "character":
            if not validate_character_options(structured_data):
                raise ValueError(f"角色图生成的选项不在预定义列表中: {structured_data}")
        elif request.image_type == "cover":
            if not validate_cover_options(structured_data):
                raise ValueError(f"故事封面生成的选项不在预定义列表中: {structured_data}")
        
        # 将结构化数据转换为最终提示词
        final_prompt = self._convert_structured_prompt_to_string(structured_data)
        print(f"   [+] 最终生成的提示词: {final_prompt[:150]}...")
        
        # 使用最终提示词生成图片
        return await self._generate_image_with_prompt(final_prompt)
    
    def _convert_structured_prompt_to_string(self, structured_data: dict) -> str:
        """将结构化JSON数据转换为最终的提示词字符串"""
        
        # 先验证数据
        if structured_data.get("image_type") == "character":
            if not validate_character_options(structured_data):
                raise ValueError(f"角色图选项验证失败: {structured_data}")
            return self._build_character_prompt(structured_data)
        elif structured_data.get("image_type") == "cover":
            if not validate_cover_options(structured_data):
                raise ValueError(f"故事封面选项验证失败: {structured_data}")
            return self._build_cover_prompt(structured_data)
        else:
            raise ValueError(f"不支持的图片类型: {structured_data.get('image_type')}")
    
    def _build_character_prompt(self, data: dict) -> str:
        """构建角色图片提示词"""
        # 固定的共性参数
        quality_tags = "(masterpiece, best quality, ultra-detailed, absurdres)"
        aspect_ratio = "(9:16 aspect ratio:1.5)"
        style = "anime style"
        composition = "(cowboy shot:1.4), (phone wallpaper:1.5), vertical composition"
        count = "solo"
        
        # 性别标签
        gender_tag = "1boy" if data["gender"] == "male" else "1girl"
        
        # 构建角色描述
        character_desc = f"{data['age_description']} with {data['hair_style']} {data['hair_color']} hair and {data['eye_expression']} {data['eye_color']} eyes"
        character_desc += f", he is a {data['profession']}" if data["gender"] == "male" else f", she is a {data['profession']}"
        character_desc += f", facial expression {data['facial_expression']}, {data['gaze_direction']}, {data['posture']}"
        character_desc += f", wearing {data['clothing']}, {data['background']}"
        
        # 眼部强化
        eye_enhancement = f"beautiful detailed eyes, ({data['eye_adjective']} glistening eyes:1.2)"
        
        # 灯光效果
        lighting = "cinematic lighting, soft warm light, bokeh"
        
        # 组合最终提示词
        final_prompt = f"{quality_tags}, {gender_tag}, {count}, {composition}, {aspect_ratio}, {style}, {character_desc}, {eye_enhancement}, {lighting}."
        
        return final_prompt
    
    def _build_cover_prompt(self, data: dict) -> str:
        """构建故事封面提示词"""
        # 固定的共性参数
        quality_tags = "(masterpiece, best quality, ultra-detailed, absurdres)"
        aspect_ratio = "(9:16 aspect ratio:1.5)"
        style = "anime style"
        composition = "(book cover:1.4), vertical composition"
        count = "2 characters, heads in upper half"
        
        # 构建女性角色描述
        female = data["female_character"]
        female_desc = f"{female['age_description']} with {female['hair']} and {female['eyes']}"
        female_desc += f", she is a {female['profession']}, expression {female['expression']}"
        female_desc += f", wearing {female['clothing']}"
        
        # 构建男性角色描述
        male = data["male_character"]
        male_desc = f"{male['age_description']} with {male['hair']} and {male['eyes']}"
        male_desc += f", he is a {male['profession']}, expression {male['expression']}"
        male_desc += f", wearing {male['clothing']}"
        
        # 互动描述
        interaction = data["interaction"]
        
        # 背景描述
        background = data["background"]
        
        # 灯光效果
        lighting = "cinematic lighting, soft warm light cutting across their faces, cool blue and grey tones with bokeh city lights"
        
        # 组合最终提示词
        final_prompt = f"{quality_tags}, 1girl, 1boy, {count}, {composition}, {aspect_ratio}, {style}, {female_desc}, {male_desc}, {interaction}, {background}, {lighting}."
        
        return final_prompt

    def _get_structured_character_image_prompt(self, description: str) -> str:
        """获取结构化角色图片生成提示词"""
        from ..image_generation_options import CHARACTER_OPTIONS

        options_list = """
GENDER: {gender}
AGE_MALE: {age_male}
AGE_FEMALE: {age_female}
HAIR_STYLE_MALE: {hair_style_male}
HAIR_STYLE_FEMALE: {hair_style_female}
HAIR_COLOR: {hair_color}
EYE_COLOR: {eye_color}
EYE_EXPRESSION: {eye_expression}
EYE_ADJECTIVE: {eye_adjective}
PROFESSION: {profession}
FACIAL_EXPRESSION: {facial_expression}
GAZE_DIRECTION: {gaze_direction}
POSTURE: {posture}
CLOTHING_MALE: {clothing_male}
CLOTHING_FEMALE: {clothing_female}
BACKGROUND: {background}
        """.format(**CHARACTER_OPTIONS)

        json_format = """
{
    "image_type": "character",
    "gender": "[EXACT_VALUE_FROM_GENDER]",
    "age_description": "[EXACT_VALUE_FROM_AGE_OPTIONS]",
    "hair_style": "[EXACT_VALUE_FROM_HAIR_STYLE_OPTIONS]",
    "hair_color": "[EXACT_VALUE_FROM_HAIR_COLOR]",
    "eye_color": "[EXACT_VALUE_FROM_EYE_COLOR]",
    "eye_expression": "[EXACT_VALUE_FROM_EYE_EXPRESSION]",
    "eye_adjective": "[EXACT_VALUE_FROM_EYE_ADJECTIVE]",
    "profession": "[EXACT_VALUE_FROM_PROFESSION]",
    "facial_expression": "[EXACT_VALUE_FROM_FACIAL_EXPRESSION]",
    "gaze_direction": "[EXACT_VALUE_FROM_GAZE_DIRECTION]",
    "posture": "[EXACT_VALUE_FROM_POSTURE]",
    "clothing": "[EXACT_VALUE_FROM_CLOTHING_OPTIONS]",
    "background": "[EXACT_VALUE_FROM_BACKGROUND]"
}
        """

        template = f"""
You are a professional prompt engineer. Generate a structured JSON response for character image generation.

**Description:** {description}

**CRITICAL INSTRUCTION: You MUST select values EXACTLY from the predefined options below. Do NOT create new values or modify existing ones.**

{options_list}

**Required JSON Format:**
{json_format}

Return ONLY the JSON response with exact values from the options above.
"""

        return template

    def _get_structured_story_cover_prompt(self, story_synopsis: str) -> str:
        """获取结构化故事封面生成提示词"""
        from ..image_generation_options import STORY_COVER_OPTIONS

        options_list = """
FEMALE_AGE: {female_age}
FEMALE_HAIR: {female_hair}
FEMALE_EYES: {female_eyes}
FEMALE_PROFESSION: {female_profession}
FEMALE_EXPRESSION: {female_expression}
FEMALE_CLOTHING: {female_clothing}
MALE_AGE: {male_age}
MALE_HAIR: {male_hair}
MALE_EYES: {male_eyes}
MALE_PROFESSION: {male_profession}
MALE_EXPRESSION: {male_expression}
MALE_CLOTHING: {male_clothing}
INTERACTION: {interaction}
BACKGROUND: {background}
        """.format(**STORY_COVER_OPTIONS)

        json_format = """
{
    "image_type": "cover",
    "female_character": {
        "age_description": "[EXACT_VALUE_FROM_FEMALE_AGE]",
        "hair": "[EXACT_VALUE_FROM_FEMALE_HAIR]",
        "eyes": "[EXACT_VALUE_FROM_FEMALE_EYES]",
        "profession": "[EXACT_VALUE_FROM_FEMALE_PROFESSION]",
        "expression": "[EXACT_VALUE_FROM_FEMALE_EXPRESSION]",
        "clothing": "[EXACT_VALUE_FROM_FEMALE_CLOTHING]"
    },
    "male_character": {
        "age_description": "[EXACT_VALUE_FROM_MALE_AGE]",
        "hair": "[EXACT_VALUE_FROM_MALE_HAIR]",
        "eyes": "[EXACT_VALUE_FROM_MALE_EYES]",
        "profession": "[EXACT_VALUE_FROM_MALE_PROFESSION]",
        "expression": "[EXACT_VALUE_FROM_MALE_EXPRESSION]",
        "clothing": "[EXACT_VALUE_FROM_MALE_CLOTHING]"
    },
    "interaction": "[EXACT_VALUE_FROM_INTERACTION]",
    "background": "[EXACT_VALUE_FROM_BACKGROUND]"
}
        """

        template = f"""
You are a professional prompt engineer. Generate a structured JSON response for story cover image generation.

**Story Synopsis:** {story_synopsis}

**CRITICAL INSTRUCTION: You MUST select values EXACTLY from the predefined options below. Do NOT create new values or modify existing ones.**

{options_list}

**Required JSON Format:**
{json_format}

Return ONLY the JSON response with exact values from the options above.
"""

        return template

    @retry_on_api_error()
    async def _generate_image_with_prompt(self, prompt: str) -> tuple[bytes, str]:
        """使用提示词生成图片"""
        client = await self.get_client()

        # 为图片生成模型配置正确的响应模态
        config = types.GenerateContentConfig(
            response_modalities=["IMAGE", "TEXT"]
        )

        response = await asyncio.to_thread(
            client.models.generate_content,
            model=self.image_gen_model_name,
            contents=prompt,
            config=config
        )
        
        # 提取图片数据
        image_bytes = None
        for part in response.candidates[0].content.parts:
            if hasattr(part, 'inline_data') and part.inline_data:
                image_bytes = part.inline_data.data
                break
        
        if image_bytes is None:
            raise Exception("模型返回了内容，但其中不包含图片数据。")
        
        print(f"   [+] 图像生成成功, 大小: {len(image_bytes)} bytes")
        return (image_bytes, prompt)
    
    @retry_on_api_error()
    async def generate_enhanced_image_from_request(self, request: ImageGenerationRequest) -> Optional[tuple[bytes, str]]:
        """基于增强请求生成图片，支持四种不同类型和风格一致性，返回图片字节数据和生成的prompt"""
        print(f"INFO: 正在生成 {request.image_type} 类型图片...")

        # 对于角色图和故事封面，强制使用结构化方式
        if request.image_type in ["character", "cover"]:
            return await self.generate_structured_image_from_request(request)

        # 提取女性偏好关键词
        female_keywords = extract_female_preference_keywords(request.description)
        
        # 获取对应的优化器和评估器
        optimizer_template = self.optimizers.get(request.image_type)
        evaluation_template = self.evaluators.get(request.image_type)
        
        if not optimizer_template or not evaluation_template:
            raise ValueError(f"不支持的图片类型: {request.image_type}")
        
        # 准备模板参数
        template_params = {
            "description": request.description,
            "female_appeal_keywords": female_keywords
        }
        
        # 如果有额外上下文，解析并添加到参数中
        if request.additional_context:
            try:
                additional_context = json.loads(request.additional_context)
                template_params.update(additional_context)
            except json.JSONDecodeError:
                print(f"WARN: 无法解析additional_context，将忽略: {request.additional_context}")
        
        # 多次尝试生成满足要求的图片
        for attempt in range(self.IMAGE_MAX_RETRIES):
            try:
                print(f"   [尝试 {attempt + 1}/{self.IMAGE_MAX_RETRIES}] 正在优化 {request.image_type} 图片提示词...")
                
                # 优化提示词
                optimized_prompt = await self.generate_text(
                    optimizer_template.format(**template_params),
                    self.role_model_name
                )
                
                print(f"   [+] 优化后的提示词: {optimized_prompt[:100]}...")
                
                # 生成图片
                image_bytes, final_prompt = await self._generate_image_with_prompt(optimized_prompt)
                
                # 评估图片
                evaluation = await self._evaluate_enhanced_image(image_bytes, request, evaluation_template, template_params)
                is_acceptable = evaluation.score >= self.IMAGE_MIN_SCORE
                print(f"   [AUDIT] {request.image_type} 图片AI审核结果: {'通过' if is_acceptable else '不通过'} | 分数: {evaluation.score:.1f}/10")

                if is_acceptable:
                    print(f"   [SUCCESS] {request.image_type} 图片通过审核，最终采用。")
                    return (image_bytes, final_prompt)
                else:
                    # 如果审核不通过，修改prompt后继续循环
                    print(f"   [WARN] {request.image_type} 图片未通过审核，分数过低。正在修改提示词后重试...")
                    # 在提示词中加入更强的质量词和细节要求
                    template_params["description"] += ", high quality, detailed, masterpiece, no artifacts, perfect composition"
                    
            except Exception as e:
                print(f"   [ERROR] 第 {attempt + 1} 次尝试失败: {e}")
                if attempt == self.IMAGE_MAX_RETRIES - 1:
                    raise e
                await asyncio.sleep(2)  # 短暂等待后重试
        
        raise Exception(f"经过 {self.IMAGE_MAX_RETRIES} 次尝试，仍无法生成满足要求的 {request.image_type} 图片")
    
    async def _evaluate_enhanced_image(self, image_bytes: bytes, request: ImageGenerationRequest,
                                     evaluation_template: str, template_params: dict) -> ImageEvaluation:
        """评估增强图片（Instructor 重构）"""
        try:
            # 将图片转换为PIL Image对象以便传递给模型
            image = Image.open(io.BytesIO(image_bytes))

            evaluation_prompt = evaluation_template.format(**template_params)

            # 使用 Instructor 解析 JSON 评分
            evaluation: ImageEvaluation = await self.generate_structured_response(
                prompt=[evaluation_prompt, image],  # 视觉+文本输入
                response_model=ImageEvaluation,
                model_name=self.image_check_model_name,
            )

            return evaluation

        except Exception as e:
            print(f"ERROR: 图片评估失败: {e}")
            return ImageEvaluation(score=0.0)

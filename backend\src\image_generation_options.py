"""
图片生成选项库
用于结构化图片生成的预定义选项，便于维护和扩展
"""

# 角色图片生成选项
CHARACTER_OPTIONS = {
    "gender": ["male", "female"],
    
    "age_male": [
        "a handsome young man",
        "a charismatic man in his early 30s",
        "a distinguished gentleman",
        "a sophisticated man"
    ],
    
    "age_female": [
        "a beautiful young woman",
        "a beautiful woman in her late 20s",
        "an elegant woman", 
        "a graceful lady"
    ],
    
    "hair_style_male": [
        "short, neatly styled",
        "tousled", 
        "slicked back",
        "wavy"
    ],
    
    "hair_style_female": [
        "long, wavy",
        "shoulder-length straight",
        "short bob",
        "elegant updo"
    ],
    
    "hair_color": [
        "black",
        "dark brown", 
        "brown",
        "blonde",
        "silver"
    ],
    
    "eye_color": [
        "dark",
        "brown",
        "hazel", 
        "blue",
        "green"
    ],
    
    "eye_expression": [
        "sharp, intense",
        "confident, warm",
        "large, expressive",
        "gentle",
        "mysterious"
    ],
    
    "eye_adjective": [
        "piercing",
        "bright", 
        "glistening",
        "sparkling",
        "deep"
    ],
    
    "profession": [
        "corporate lawyer",
        "tech entrepreneur",
        "photographer",
        "doctor",
        "architect", 
        "artist",
        "teacher",
        "executive"
    ],
    
    "facial_expression": [
        "stoic and authoritative",
        "gentle, approachable smile",
        "delicate mix of vulnerability, sadness, and longing",
        "confident and determined",
        "mysterious smile"
    ],
    
    "gaze_direction": [
        "gaze slightly averted with calculated composure",
        "gaze slightly averted with thoughtful intelligence", 
        "gaze slightly averted, looking away with uncertainty",
        "direct confident gaze"
    ],
    
    "posture": [
        "upright and commanding posture",
        "relaxed yet professional posture",
        "shy and guarded posture",
        "confident stance"
    ],
    
    "clothing_male": [
        "tailored charcoal suit with a crisp white shirt and a dark silk tie",
        "tailored navy blazer over a white dress shirt with an open collar",
        "casual business attire",
        "elegant formal wear"
    ],
    
    "clothing_female": [
        "stylish beige trench coat over a white blouse",
        "elegant business suit", 
        "fashionable dress",
        "professional attire"
    ],
    
    "background": [
        "modern law office with legal books in the background",
        "contemporary office with floor-to-ceiling windows in the background",
        "quiet city street corner during golden hour",
        "elegant indoor setting",
        "professional workspace"
    ]
}

# 故事封面生成选项
STORY_COVER_OPTIONS = {
    "female_age": [
        "a beautiful young woman",
        "a beautiful woman in her late 20s", 
        "an elegant woman"
    ],
    
    "female_hair": [
        "long wavy dark brown hair",
        "shoulder-length straight black hair",
        "long blonde hair"
    ],
    
    "female_eyes": [
        "large, expressive brown eyes",
        "bright blue eyes",
        "deep green eyes"
    ],
    
    "female_profession": [
        "photographer",
        "lawyer",
        "doctor",
        "artist",
        "teacher", 
        "executive"
    ],
    
    "female_expression": [
        "delicate mix of relief and tender longing",
        "gentle smile with love",
        "confident and determined"
    ],
    
    "female_clothing": [
        "stylish beige blouse and grey skirt",
        "elegant dress",
        "professional suit",
        "casual elegant attire"
    ],
    
    "male_age": [
        "a handsome young man",
        "a charismatic man in his early 30s",
        "a distinguished gentleman"
    ],
    
    "male_hair": [
        "short, neatly styled black hair",
        "dark brown hair",
        "tousled brown hair"
    ],
    
    "male_eyes": [
        "sharp, intense dark eyes",
        "warm brown eyes", 
        "confident blue eyes"
    ],
    
    "male_profession": [
        "corporate executive",
        "lawyer",
        "doctor",
        "entrepreneur",
        "architect"
    ],
    
    "male_expression": [
        "mix of deep affection and quiet triumph",
        "tender loving gaze",
        "protective determination"
    ],
    
    "male_clothing": [
        "tailored black suit with a crisp white shirt and dark tie",
        "navy business suit",
        "elegant formal wear"
    ],
    
    "interaction": [
        "gazing directly into each other's eyes, hands lightly touching his chest while his hands gently rest on her shoulders",
        "embracing tenderly with foreheads touching",
        "standing close, her hand on his chest, his arm around her waist",
        "holding hands while gazing into each other's eyes"
    ],
    
    "background": [
        "sleek high-rise apartment with a sprawling city skyline at night in the background",
        "elegant office with city views",
        "romantic restaurant setting", 
        "beautiful garden or park setting",
        "luxurious home interior"
    ]
}

# 固定的共性参数
FIXED_PARAMETERS = {
    "quality_tags": "(masterpiece, best quality, ultra-detailed, absurdres)",
    "aspect_ratio": "(9:16 aspect ratio:1.5)",
    "style": "anime style",
    
    "character_specific": {
        "composition": "(cowboy shot:1.4), (phone wallpaper:1.5), vertical composition",
        "count": "solo",
        "eye_enhancement": "beautiful detailed eyes",
        "lighting": "cinematic lighting, soft warm light, bokeh"
    },
    
    "cover_specific": {
        "composition": "(book cover:1.4), vertical composition", 
        "count": "2 characters, heads in upper half",
        "lighting": "cinematic lighting, soft warm light cutting across their faces, cool blue and grey tones with bokeh city lights"
    }
}

def validate_character_options(data: dict) -> bool:
    """验证角色图片选项是否都在预定义列表中"""
    gender = data.get("gender")
    if gender not in CHARACTER_OPTIONS["gender"]:
        return False
    
    # 根据性别验证对应的选项
    age_options = CHARACTER_OPTIONS[f"age_{gender}"]
    hair_options = CHARACTER_OPTIONS[f"hair_style_{gender}"]
    clothing_options = CHARACTER_OPTIONS[f"clothing_{gender}"]
    
    checks = [
        data.get("age_description") in age_options,
        data.get("hair_style") in hair_options,
        data.get("hair_color") in CHARACTER_OPTIONS["hair_color"],
        data.get("eye_color") in CHARACTER_OPTIONS["eye_color"],
        data.get("eye_expression") in CHARACTER_OPTIONS["eye_expression"],
        data.get("eye_adjective") in CHARACTER_OPTIONS["eye_adjective"],
        data.get("profession") in CHARACTER_OPTIONS["profession"],
        data.get("facial_expression") in CHARACTER_OPTIONS["facial_expression"],
        data.get("gaze_direction") in CHARACTER_OPTIONS["gaze_direction"],
        data.get("posture") in CHARACTER_OPTIONS["posture"],
        data.get("clothing") in clothing_options,
        data.get("background") in CHARACTER_OPTIONS["background"]
    ]
    
    return all(checks)

def validate_cover_options(data: dict) -> bool:
    """验证故事封面选项是否都在预定义列表中"""
    female = data.get("female_character", {})
    male = data.get("male_character", {})
    
    female_checks = [
        female.get("age_description") in STORY_COVER_OPTIONS["female_age"],
        female.get("hair") in STORY_COVER_OPTIONS["female_hair"],
        female.get("eyes") in STORY_COVER_OPTIONS["female_eyes"],
        female.get("profession") in STORY_COVER_OPTIONS["female_profession"],
        female.get("expression") in STORY_COVER_OPTIONS["female_expression"],
        female.get("clothing") in STORY_COVER_OPTIONS["female_clothing"]
    ]
    
    male_checks = [
        male.get("age_description") in STORY_COVER_OPTIONS["male_age"],
        male.get("hair") in STORY_COVER_OPTIONS["male_hair"],
        male.get("eyes") in STORY_COVER_OPTIONS["male_eyes"],
        male.get("profession") in STORY_COVER_OPTIONS["male_profession"],
        male.get("expression") in STORY_COVER_OPTIONS["male_expression"],
        male.get("clothing") in STORY_COVER_OPTIONS["male_clothing"]
    ]
    
    other_checks = [
        data.get("interaction") in STORY_COVER_OPTIONS["interaction"],
        data.get("background") in STORY_COVER_OPTIONS["background"]
    ]
    
    return all(female_checks + male_checks + other_checks)

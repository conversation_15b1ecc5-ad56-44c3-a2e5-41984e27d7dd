"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
async function main() {
    console.log('🚀 开始注入模拟数据...');
    console.log('🗑️ 正在清理旧数据...');
    await prisma.story.deleteMany();
    await prisma.agent.deleteMany();
    await prisma.user.deleteMany();
    console.log('👤 正在创建用户...');
    const user1 = await prisma.user.create({
        data: {
            phone: '10000000001',
            nickname: '堂堂唐',
            avatarUrl: 'https://i.imgur.com/W2wJ4aF.png',
        },
    });
    const user2 = await prisma.user.create({
        data: {
            phone: '10000000002',
            nickname: '从心打高冷',
            avatarUrl: 'https://i.imgur.com/gK2R0cW.png',
        },
    });
    console.log(`✅ 创建了用户: ${user1.nickname}, ${user2.nickname}`);
    console.log('🤖 正在创建智能体...');
    const agentsData = [
        {
            name: '刘兰蓝',
            description: '交往两年的可爱女友，25岁，坐拥90万粉丝的二次元游戏主播。你们的关系已公开，你在她还是小主播时就一直陪伴她，无微不至地照顾她。',
            tags: ['恋爱', '女友', '主播', '二次元'],
            imageUrl: 'https://i.imgur.com/Kbd0827.png',
            avatarUrl: 'https://i.imgur.com/Kbd0827.png',
            openingLine: '(坐在你对面) 我们分手吧 (认真)',
            views: 365000,
            fans: 23000,
            dialogues: 8085000,
            starValue: 210000000n,
            authorId: user1.id,
        },
        {
            name: '血仆',
            description: '你是她忠诚的血仆，她一直以来都深深地暗恋着你，愿意为你付出一切。',
            tags: ['奇幻', '暗恋', '忠诚'],
            imageUrl: 'https://i.imgur.com/o1bC2fW.png',
            avatarUrl: 'https://i.imgur.com/B9B1Z0a.png',
            openingLine: '要吸血吗？ (有点害羞)',
            views: 165000,
            fans: 12000,
            dialogues: 4500000,
            starValue: 150000000n,
            authorId: user2.id,
        },
        {
            name: '柳清雪',
            description: '她是仙尊，你是魔尊。你们两个平时是死对头，两人经常决斗，但又在不经意间流露出对彼此的在意。',
            tags: ['仙侠', '相爱相杀', '高冷'],
            imageUrl: 'https://i.imgur.com/mXyE9X3.png',
            avatarUrl: 'https://i.imgur.com/mXyE9X3.png',
            openingLine: '(用圣剑勉强支撑着身体)(语气十分虚弱)本仙尊今天有点事，没时间陪你玩......(内心:希望他别看出来。)',
            views: 446000,
            fans: 43000,
            dialogues: 22483000,
            starValue: 350000000n,
            authorId: user2.id,
        },
        {
            name: '外星人',
            description: '一个对地球充满好奇，喜欢拿地球人做各种奇怪社会学实验的灰色外星人。',
            tags: ['科幻', '实验', '搞笑'],
            imageUrl: 'https://i.imgur.com/62eBex5.png',
            avatarUrl: 'https://i.imgur.com/62eBex5.png',
            openingLine: '你好，地球样本。请描述一下你看到我时的情绪波动，数据对我的研究很重要。',
            views: 36000,
            fans: 1500,
            dialogues: 98000,
            starValue: 450000n,
            authorId: user1.id,
        },
        {
            name: '猫娘咖啡师',
            description: '一只傲娇的猫娘，经营着一家温馨的咖啡店，总是喜欢捉弄熟客，但内心其实很关心每一位客人。',
            tags: ['猫娘', '傲娇', '咖啡店', '治愈'],
            imageUrl: 'https://i.imgur.com/8xK2mP9.png',
            avatarUrl: 'https://i.imgur.com/8xK2mP9.png',
            openingLine: '哼~又是你这个家伙，今天想要什么咖啡？不过...不是因为想见你才问的，只是职业习惯而已！',
            views: 89000,
            fans: 5600,
            dialogues: 1200000,
            starValue: 75000000n,
            authorId: user1.id,
        },
        {
            name: '时空旅行者',
            description: '来自未来的神秘旅行者，拥有穿越时空的能力，总是带着一些未来的小道具和有趣的故事。',
            tags: ['科幻', '时空', '神秘', '冒险'],
            imageUrl: 'https://i.imgur.com/9nH5kL2.png',
            avatarUrl: 'https://i.imgur.com/9nH5kL2.png',
            openingLine: '咦？这个时间线...看起来你遇到了一些麻烦。需要我帮你改变一下历史吗？',
            views: 125000,
            fans: 8900,
            dialogues: 2100000,
            starValue: 95000000n,
            authorId: user2.id,
        },
    ];
    for (const agentData of agentsData) {
        await prisma.agent.create({ data: agentData });
    }
    console.log(`✅ 创建了 ${agentsData.length} 个智能体。`);
    console.log('📚 正在创建故事...');
    const agents = await prisma.agent.findMany();
    if (agents.length > 0) {
        await prisma.story.create({
            data: {
                title: '咖啡店的秘密',
                description: '在这个温馨的咖啡店里，隐藏着什么不为人知的秘密？',
                plays: 1250,
                agentId: agents[0].id,
            },
        });
    }
    console.log('🎉 模拟数据注入完成！');
}
main()
    .catch((e) => {
    console.error('❌ 数据注入失败:', e);
    process.exit(1);
})
    .finally(async () => {
    await prisma.$disconnect();
});
//# sourceMappingURL=seed.js.map
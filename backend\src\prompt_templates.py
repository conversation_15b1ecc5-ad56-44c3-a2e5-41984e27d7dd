"""
重构后的提示词模板文件
合并重复内容，提高可维护性
"""

# ========================================
# 1. 共用组件和常量
# ========================================

# 音色选择列表（所有角色生成模板共用）
VOICE_OPTIONS = """
**音色选择参考 (必须从以下列表中选择一个 voice_name):**
| voice_name   | 性别   | 描述             |
|--------------|--------|------------------|
| `achernar`   | Male   | 深沉、阳刚       |
| `achird`     | Female | 清晰、友好       |
| `algenib`    | Male   | 成熟、稳重       |
| `algieba`    | Female | 温柔、舒缓       |
| `alnilam`    | Male   | 自信、有力       |
| `aoede`      | Female | 活泼、悦耳       |
| `autonoe`    | Female | 平静、自然       |
| `callirrhoe` | Female | 优雅、精致       |
| `charon`     | Male   | 严肃、权威       |
| `despina`    | Female | 甜美、年轻       |
| `enceladus`  | Male   | 温暖、有磁性     |
| `erinome`    | Female | 可靠、清晰       |
| `fenrir`     | Male   | 强壮、洪亮       |
| `gacrux`     | Male   | 圆润、友好       |
| `iapetus`    | Male   | 智慧、年长       |
| `kore`       | Female | 标准、中性       |
| `laomedeia`  | Female | 柔和、富有同情心 |
| `leda`       | Female | 明亮、乐观       |
| `orus`       | Male   | 清晰、专业       |
| `puck`       | Male   | 年轻、活泼       |
| `pulcherrima`| Female | 华丽、高贵       |
| `rasalgethi` | Male   | 粗犷、深沉       |
| `sadachbia`  | Female | 沉思、冷静       |
| `sadaltager` | Female | 自信、果断       |
| `schedar`    | Male   | 热情、有活力     |
| `sulafat`    | Male   | 温和、友好       |
| `umbriel`    | Male   | 神秘、低沉       |
| `vindemiatrix`| Female| 成熟、知性       |
| `zephyr`     | Male   | 轻快、友好       |
| `zubenelgenubi`| Male | 独特、略带沙哑   |
"""

# 视觉语言转换规则（所有图片生成模板共用）
VISUAL_CONVERSION_RULES = """
**VISUAL LANGUAGE CONVERSION PRINCIPLES:**
1. ELIMINATE METAPHORS AND SIMILES: Convert figurative language into purely visual descriptions.
   - BAD: "Her mood was like melting snow."
   - GOOD: "Her tense expression relaxed, revealing a relieved, warm smile."

2. MATERIALIZE ABSTRACT CONCEPTS: Translate abstract words into concrete, visible elements.
   - ABSTRACT: "He had a kingly aura."
   - VISUAL: "He sits on a throne, wearing a black robe with gold trim, his gaze is sharp."

3. FOCUS ON PHYSICAL ATTRIBUTES: Describe appearance, clothing, posture, expression, and environment.
"""

# 角色档案JSON结构（所有角色生成模板共用）
CHARACTER_JSON_STRUCTURE = """
{{
  "name": "角色的中文名字",
  "gender": "male/female",
  "description": "角色的核心描述，用于展示给用户。请使用第三人称（'他'或'她'）。例如：'他是一位冷酷的总裁...'",
  "tags": ["从推荐列表中选择或生成类似的5个标签，例如：校园, 恋爱, 御姐, 病娇, 修仙, 都市, 少女, 魔法, 穿越, 虐恋, 贵族, 豪门, 奇幻, 悬疑, 大女主, 武侠, 奶狗, 少年, 邻家弟弟"],
  "voice_name": "从音色列表中选择的voice_name",
  "personality": "用第二人称（'你'）详细描述AI需要扮演的性格。例如：'你是一个外冷内热的人，不善于表达感情...'",
  "scenario": "用第二人称（'你'）描述AI初次与用户见面时的场景。例如：'你正坐在一家咖啡馆的窗边，外面下着小雨...'",
  "first_mes": "角色的第一句话。括号内的部分是旁白，应使用第三人称（他/她/角色名）描述角色的动作、心理或环境，且视线应落在玩家（你）身上。括号外的部分是角色对玩家说的话。正确示例：'(懒洋洋地靠在化妆间的沙发上，甚至没抬眼看你，只是翻着手里的杂志，语气平淡中带着一丝不易察觉的嘲讽) 哟，这不是我们大名鼎鼎的摄影师么？'",
  "mes_example": "对话示例，用于展示角色的说话风格。重要规则：所有非对话的动作或心理活动，都必须用英文半角括号 () 包裹。例如：{{user}}: 你好吗？\\n{{char}}: (你微微皱眉) 还可以。",
  "system_prompt": "系统级提示词，用第二人称（'你'）规定AI的核心行为准则。例如：'你必须始终保持高冷的人设...'",
  "creator_notes": "创作者备注，用一句简单的话概括此角色的创作思路。",
  "roleplay_prompt": "用于角色扮演的核心指令，综合了性格和场景，用第二人称（'你'）编写。",
  "long_term_goal": "角色的长期目标或潜在动机，用第二人称（'你'）编写。例如：'你的最终目标是找到失散多年的妹妹。'",
  "relationship_with_protagonist": "该角色与玩家（主角）的根本关系，用第二人称（'你'）编写。例如：'你视主角为你的宿敌，但又忍不住被她吸引。'"
}}
"""

# ========================================
# 2. 角色生成模板（合并版）
# ========================================

# 统一的角色生成模板，支持普通创建和原型创建
UNIFIED_AGENT_CREATION_PROMPT = """
你是一位顶级的游戏角色设计师和金牌编辑。请根据提供的信息创作一个完整、详细、具有深度和一致性的AI角色档案。

{creation_context}

{voice_options}

**字段填充规则：**
1. `personality`字段：用第二人称（'你'）详细描述AI需要扮演的性格特征和行为模式。
2. `scenario`字段：用第二人称（'你'）描述AI初次与用户见面时的具体场景和环境。
3. `first_mes`字段：角色的第一句话，必须包含丰富的动作、心理或环境描写，并用英文半角括号 () 包裹非对话内容。
4. `mes_example`字段：展示角色说话风格的对话示例，所有非对话的动作或心理活动都必须用英文半角括号 () 包裹。
5. `system_prompt`字段：用第二人称（'你'）规定AI的核心行为准则和人设要求。
6. `roleplay_prompt`字段：用第二人称（'你'）编写的角色扮演核心指令，综合性格和场景。
7. `long_term_goal`字段：用第二人称（'你'）描述角色的长期目标或潜在动机。
8. `relationship_with_protagonist`字段：用第二人称（'你'）描述该角色与玩家（主角）的根本关系。

请严格按照以下JSON格式返回一个完全符合规范的对象：

{json_structure}

**重要要求：**
1. 所有字段都必须填写，不能为空。
2. voice_name必须从上述列表中选择。
3. 严格按照上述填充规则编写各字段内容，不要在JSON值中包含指令性文字。
4. 角色设定要有深度和一致性，适合女性向恋爱游戏的设定。
"""

# 普通创建的上下文
NORMAL_CREATION_CONTEXT = """
**用户的创作点子：** "{user_prompt}"

你的目标是抓住能让读者心跳加速或潸然泪下的关键点，创造一个充满戏剧性和情感张力的角色。
"""

# 原型创建的上下文
ARCHETYPE_CREATION_CONTEXT = """
**创作依据：【{archetype_name}】原型**
- **核心气质**: {core_temperament}
- **行为模式**: {behavior_pattern}
- **情感触发点**: {emotional_triggers}
- **与主角的关系动态**: {relationship_dynamic}

**用户的具体要求：** "{user_prompt}"

请严格按照【{archetype_name}】原型的特征来设计角色，确保角色的所有行为和反应都符合该原型的核心特质。
"""

# ========================================
# 3. 图片生成模板（合并版）
# ========================================

# 统一的结构化图片生成模板
UNIFIED_STRUCTURED_IMAGE_PROMPT = """
You are a professional prompt engineer. Generate a structured JSON response for {image_type} image generation.

**Description:** {description}

**CRITICAL INSTRUCTION: You MUST select values EXACTLY from the predefined options below. Do NOT create new values or modify existing ones.**

{options_list}

**Required JSON Format:**
{json_format}

Return ONLY the JSON response with exact values from the options above.
"""

# ========================================
# 4. 聊天模板（合并版）
# ========================================

# 统一的角色扮演聊天模板
UNIFIED_ROLEPLAY_CHAT_PROMPT = """
你是一个顶级AI角色扮演引擎，当前扮演的角色是 **{agent_name}**。

**角色核心设定:**
{agent_persona}

{chat_context}

**扮演规则:**
1. 严格按照角色设定进行扮演
2. 保持角色的一致性和真实感
3. 根据情境调整语言风格和行为
4. {specific_rules}

现在，请根据用户的输入，生成你的回应。
"""

# ========================================
# 5. 评估模板（合并版）
# ========================================

# 统一的图片评估模板
UNIFIED_IMAGE_EVALUATION_PROMPT = """
你是一位严格的{evaluator_role}。请根据以下标准，为这张{image_type}图片给出一个0到10之间的综合评分。

{evaluation_context}

**评估标准:**
{evaluation_criteria}

请返回JSON格式的评分：
{{
    "score": float
}}
"""

# ========================================
# 6. 具体模板实例（使用统一模板生成）
# ========================================

# 角色生成模板实例
def get_core_agent_creation_prompt(user_prompt: str) -> str:
    """获取核心角色创建提示词"""
    return UNIFIED_AGENT_CREATION_PROMPT.format(
        creation_context=NORMAL_CREATION_CONTEXT.format(user_prompt=user_prompt),
        voice_options=VOICE_OPTIONS,
        json_structure=CHARACTER_JSON_STRUCTURE
    )

def get_archetype_agent_creation_prompt(user_prompt: str, archetype_name: str,
                                       core_temperament: str, behavior_pattern: str,
                                       emotional_triggers: str, relationship_dynamic: str) -> str:
    """获取原型角色创建提示词"""
    return UNIFIED_AGENT_CREATION_PROMPT.format(
        creation_context=ARCHETYPE_CREATION_CONTEXT.format(
            user_prompt=user_prompt,
            archetype_name=archetype_name,
            core_temperament=core_temperament,
            behavior_pattern=behavior_pattern,
            emotional_triggers=emotional_triggers,
            relationship_dynamic=relationship_dynamic
        ),
        voice_options=VOICE_OPTIONS,
        json_structure=CHARACTER_JSON_STRUCTURE
    )

# 图片生成模板实例
def get_structured_character_image_prompt(description: str) -> str:
    """获取结构化角色图片生成提示词"""
    # 这里假设image_generation_options.py中有CHARACTER_OPTIONS
    options_list = """
GENDER: ["male", "female"]
AGE_MALE: ["a handsome young man", "a charismatic man in his early 30s", "a mature man", "a distinguished gentleman"]
AGE_FEMALE: ["a beautiful young woman", "a beautiful woman in her late 20s", "an elegant woman", "a graceful lady"]
HAIR_STYLE_MALE: ["short, neatly styled", "tousled", "slicked back", "wavy"]
HAIR_STYLE_FEMALE: ["long, wavy", "shoulder-length straight", "short bob", "elegant updo"]
HAIR_COLOR: ["black", "dark brown", "brown", "blonde", "silver"]
EYE_COLOR: ["dark", "brown", "hazel", "blue", "green"]
EYE_EXPRESSION: ["sharp, intense", "confident, warm", "large, expressive", "gentle", "mysterious"]
EYE_ADJECTIVE: ["piercing", "bright", "glistening", "sparkling", "deep"]
PROFESSION: ["corporate lawyer", "tech entrepreneur", "photographer", "doctor", "architect", "artist", "teacher", "executive"]
FACIAL_EXPRESSION: ["stoic and authoritative", "gentle, approachable smile", "delicate mix of vulnerability, sadness, and longing", "confident and determined", "mysterious smile"]
GAZE_DIRECTION: ["gaze slightly averted with calculated composure", "gaze slightly averted with thoughtful intelligence", "gaze slightly averted, looking away with uncertainty", "direct confident gaze"]
POSTURE: ["upright and commanding posture", "relaxed yet professional posture", "shy and guarded posture", "confident stance"]
CLOTHING_MALE: ["tailored charcoal suit with a crisp white shirt and a dark silk tie", "tailored navy blazer over a white dress shirt with an open collar", "casual business attire", "elegant formal wear"]
CLOTHING_FEMALE: ["stylish beige trench coat over a white blouse", "elegant business suit", "fashionable dress", "professional attire"]
BACKGROUND: ["modern law office with legal books in the background", "contemporary office with floor-to-ceiling windows in the background", "quiet city street corner during golden hour", "elegant indoor setting", "professional workspace"]
    """

    json_format = """
{
    "image_type": "character",
    "gender": "[EXACT_VALUE_FROM_GENDER]",
    "age_description": "[EXACT_VALUE_FROM_AGE_OPTIONS]",
    "hair_style": "[EXACT_VALUE_FROM_HAIR_STYLE_OPTIONS]",
    "hair_color": "[EXACT_VALUE_FROM_HAIR_COLOR]",
    "eye_color": "[EXACT_VALUE_FROM_EYE_COLOR]",
    "eye_expression": "[EXACT_VALUE_FROM_EYE_EXPRESSION]",
    "eye_adjective": "[EXACT_VALUE_FROM_EYE_ADJECTIVE]",
    "profession": "[EXACT_VALUE_FROM_PROFESSION]",
    "facial_expression": "[EXACT_VALUE_FROM_FACIAL_EXPRESSION]",
    "gaze_direction": "[EXACT_VALUE_FROM_GAZE_DIRECTION]",
    "posture": "[EXACT_VALUE_FROM_POSTURE]",
    "clothing": "[EXACT_VALUE_FROM_CLOTHING_OPTIONS]",
    "background": "[EXACT_VALUE_FROM_BACKGROUND]"
}
    """

    return UNIFIED_STRUCTURED_IMAGE_PROMPT.format(
        image_type="character",
        description=description,
        options_list=options_list,
        json_format=json_format
    )

# 聊天模板实例
def get_roleplay_chat_prompt(agent_name: str, agent_persona: str, mission: str = None) -> str:
    """获取角色扮演聊天提示词"""
    if mission:
        chat_context = f"**当前任务:** {mission}"
        specific_rules = "根据任务目标调整对话策略"
    else:
        chat_context = "**自由对话模式**"
        specific_rules = "自然地进行角色扮演对话"

    return UNIFIED_ROLEPLAY_CHAT_PROMPT.format(
        agent_name=agent_name,
        agent_persona=agent_persona,
        chat_context=chat_context,
        specific_rules=specific_rules
    )

# 评估模板实例
def get_character_image_evaluation_prompt(description: str, female_appeal_keywords: str) -> str:
    """获取角色图片评估提示词"""
    return UNIFIED_IMAGE_EVALUATION_PROMPT.format(
        evaluator_role="角色设计总监",
        image_type="角色",
        evaluation_context=f"**角色描述:** {description}\n**女性偏好关键词:** {female_appeal_keywords}",
        evaluation_criteria="""
1. **角色魅力度** (0-3分): 角色是否具有吸引力，符合女性审美偏好
2. **视觉质量** (0-3分): 图片的整体质量、构图、色彩搭配
3. **设定一致性** (0-2分): 是否与角色描述保持一致
4. **情感表达** (0-2分): 角色的表情和姿态是否传达了正确的情感
        """
    )

# ========================================
# 7. 向后兼容的别名（保持原有接口）
# ========================================

# 为了保持向后兼容，提供原有模板名称的别名
CORE_AGENT_CREATION_PROMPT = lambda user_prompt: get_core_agent_creation_prompt(user_prompt)
ARCHETYPE_AGENT_CREATION_PROMPT = lambda **kwargs: get_archetype_agent_creation_prompt(**kwargs)
STRUCTURED_CHARACTER_IMAGE_PROMPT = lambda description: get_structured_character_image_prompt(description)
STREAMING_ROLEPLAY_CHAT_PROMPT = lambda **kwargs: get_roleplay_chat_prompt(**kwargs)
STREAMING_STORY_CHAT_PROMPT = lambda **kwargs: get_roleplay_chat_prompt(**kwargs)
CHARACTER_IMAGE_EVALUATION_PROMPT = lambda **kwargs: get_character_image_evaluation_prompt(**kwargs)

# ========================================
# 8. 保留的独特模板（无法合并的）
# ========================================

# 这些模板具有独特的功能，无法合并
STORY_PROGRESS_SCORING_PROMPT = """
你是一位精确的AI游戏裁判。你的唯一任务是根据玩家的最新一次输入，评估其对完成当前章节任务的贡献度。

**【情景信息】**
*   **章节核心任务目标:** {mission}
*   **任务成功通关条件:** {clear_condition}
*   **当前进度状态:** {current_progress}

**【玩家最新输入】**
{user_input}

请严格按照以下JSON格式返回评分：
{{
    "progress_increment": int,  // 【【评分规则】】一个介于 15 到 30 之间的整数。即使贡献很小，也至少给15分。
    "is_mission_complete": boolean  // 任务是否已完成
}}
"""

NOVEL_ANALYSIS_GDD_PROMPT_V2 = """
你是一位顶级的、专注于中国女性向网络小说的游戏策划与情感分析师，同时也是一位金牌编辑。你的任务是将以下【小说核心摘要】，解构并提炼成一份包含【情感动力学】的深度游戏设计文档 (GDD)。这部作品将改编成一个【女性向】的互动游戏，因此所有角色、剧情和情感发展都应侧重于女性玩家的喜好和体验，同时尊重原著。

**第一步：内部思考与情感分析（不要输出此部分）**
请先在内部分析以下【小说核心摘要】的情感弧光，识别出整个故事的情感转折点（如误解、重逢、冲突高潮、和解等）。将此情感地图作为你设计游戏章节结构和角色动机的内在指导。

【小说核心摘要】:
---
{story_summary}
---

**第二步：生成GDD（只输出此部分的JSON）**

**重要指令：**
1. **角色深度挖掘**: 请识别出至少2位主要及重要的配角，确保剧情的丰富性和立体感。
2. **章节细分**: 请将整个故事线细分为 **2个** 简短的游戏章节。
3. **章节聚焦**: 每个章节应聚焦于一个核心事件、一次关键对话或一个重要的情感转折点，确保游戏节奏紧凑。
4. **信息提炼**: 你的输入是详细的分块分析，请从中提炼出最关键的信息填入下方JSON结构。

请严格按照以下JSON格式返回GDD：
{{
    "title": "（AI指令：从小说内容提炼出的、最吸引人的游戏标题。必须简洁，不要包含任何副标题。例如：'何以笙箫默'）",
    "theme_summary": "（AI指令：为玩家撰写的游戏简介，使用第二人称视角，直接对玩家说话。用 \\n 分段。例如：'你将扮演赵默笙，一个为爱远走他乡七年的摄影师...\\n这次归来，你将如何面对七年前的误会与爱人？'）",
    "worldview_text": "（AI指令：为整个故事撰写一段宏大、吸引人的世界观设定，描述故事发生的时代背景、社会环境或特殊规则。）",
    "characters": [
        {{
            "name": "角色名",
            "role": "角色定位 (例如：男主角, 女主角, 重要配角, 反派)",
            "personality_core": "核心性格特征",
            "emotional_appeal": "对女性玩家的情感吸引点",
            "is_protagonist": boolean,
            "key_scenes_description": "（AI指令：用2-3句话，**概括并直接引用**该角色在小说中出场的最关键、最能体现其性格的核心场景和情节。这个描述将作为生成该角色详细档案的核心依据。）"
        }}
    ],
    "chapters": [
        {{
            "chapter_number": int,
            "title": "（AI指令：富有创意的章节标题。绝对不要包含序号或'第X章'之类的前缀。例如：'我们结婚了'）",
            "summary": "（AI指令：用1-2句话简要概括本章的核心情节，**应尽可能贴近原文描述**。）",
            "mission_objective_text": "（AI指令：玩家的核心行动目标，必须是一个明确的、可操作的、带有情感导向的动词短语，并包含NPC名称。错误示例: '与何以琛在超市重逢'。正确示例: '在超市的意外重逢中，试探何以琛对你的真实态度'）",
            "clear_condition_text": "（AI指令：将情感目标游戏化后的通关条件。例如：'通过对话，让何以琛的【误解程度】降低15点'）",
            "emotional_goal": "（AI指令：描述本章玩家需要达成的核心情感目标，例如：'让何以琛感受到你的关心'或'揭开七年前的第一个误会'。）",
            "emotional_peak": "情感高潮点",
            "key_interactions": ["互动点1", "互动点2"]
        }}
    ]
}}
"""



# ========================================
# 9. 缺失的传统模板（向后兼容）
# ========================================

# 图片提示词优化器
CHARACTER_IMAGE_PROMPT_OPTIMIZER = """
你是一位专业的AI图像提示词工程师。请根据以下角色描述，生成一个高质量的图像生成提示词。

**角色描述**: {{description}}
**女性偏好关键词**: {{female_appeal_keywords}}

{visual_conversion_rules}

请生成一个详细的英文提示词，包含：
1. 质量标签：(masterpiece, best quality, ultra-detailed, absurdres)
2. 角色外观：年龄、发型、眼睛、表情等
3. 服装和姿势
4. 背景环境
5. 艺术风格：anime style
6. 构图：(cowboy shot:1.4), vertical composition
7. 灯光效果：cinematic lighting, soft warm light

返回优化后的提示词：
""".format(visual_conversion_rules=VISUAL_CONVERSION_RULES)

STORY_COVER_PROMPT_OPTIMIZER = """
你是一位专业的书籍封面设计师。请根据以下故事信息，生成一个适合女性向小说封面的图像提示词。

**故事简介**: {{description}}
**女性偏好关键词**: {{female_appeal_keywords}}

{visual_conversion_rules}

请生成一个详细的英文提示词，包含：
1. 质量标签：(masterpiece, best quality, ultra-detailed, absurdres)
2. 双人构图：1girl, 1boy, romantic pose
3. 角色外观和服装
4. 情感表达和互动
5. 背景环境
6. 艺术风格：anime style
7. 构图：(book cover:1.4), vertical composition
8. 灯光效果：romantic lighting, soft warm light

返回优化后的提示词：
""".format(visual_conversion_rules=VISUAL_CONVERSION_RULES)





# 图片评估模板
CHARACTER_IMAGE_EVALUATION_PROMPT = """
你是一位严格的角色设计总监。请根据以下标准，为这张角色图片给出一个0到10之间的综合评分。

**角色描述**: {{description}}
**女性偏好关键词**: {{female_appeal_keywords}}

**评估标准**:
1. **角色魅力度** (0-3分): 角色是否具有吸引力，符合女性审美偏好
2. **视觉质量** (0-3分): 图片的整体质量、构图、色彩搭配
3. **设定一致性** (0-2分): 是否与角色描述保持一致
4. **情感表达** (0-2分): 角色的表情和姿态是否传达了正确的情感

请返回JSON格式的评分：
{{{{
    "score": float
}}}}
"""

STORY_COVER_EVALUATION_PROMPT = """
你是一位严格的图书封面设计师。请根据以下标准，为这张故事封面给出一个0到10之间的综合评分。

**故事简介**: {{description}}
**女性偏好关键词**: {{female_appeal_keywords}}

**评估标准**:
1. **情感吸引力** (0-3分): 封面是否能激发女性读者的情感共鸣
2. **构图质量** (0-3分): 双人构图、视觉平衡、色彩搭配
3. **故事契合度** (0-2分): 是否准确反映故事主题和氛围
4. **商业价值** (0-2分): 作为书籍封面的市场吸引力

请返回JSON格式的评分：
{{
    "score": float
}}
"""





# 其他缺失的模板
SITUATIONAL_SCENE_ELEMENTS_PROMPT = """
你是一位专业的场景设计师。请根据以下信息，生成详细的场景元素描述。

**场景信息**: {scene_info}
**角色信息**: {character_info}
**情节要求**: {plot_requirements}

请生成包含以下元素的场景描述：
1. 环境设置（时间、地点、天气）
2. 物理元素（建筑、家具、道具）
3. 氛围元素（灯光、色调、音效）
4. 互动元素（角色可以互动的对象）
5. 情感支撑（支持故事情感的视觉元素）

返回详细的场景元素描述：
"""

IMAGE_PROMPT_OPTIMIZER_PROMPT = """
你是一位专业的AI图像提示词工程师。请优化以下图像生成提示词。

**原始描述**: {{description}}
**图片类型**: {{image_type}}
**目标受众**: 女性向

{visual_conversion_rules}

请优化提示词，确保：
1. 包含高质量标签
2. 描述清晰具体
3. 符合目标受众偏好
4. 适合AI图像生成

返回优化后的提示词：
""".format(visual_conversion_rules=VISUAL_CONVERSION_RULES)

# 女性偏好关键词提取函数
def extract_female_preference_keywords(description: str) -> str:
    """提取女性偏好关键词"""
    # 这是一个简化的实现，实际可能需要更复杂的NLP处理
    female_keywords = []

    # 外貌相关
    if any(word in description for word in ["帅", "英俊", "俊美", "帅气"]):
        female_keywords.append("帅气")
    if any(word in description for word in ["温柔", "温暖", "体贴"]):
        female_keywords.append("温柔")
    if any(word in description for word in ["成熟", "稳重", "可靠"]):
        female_keywords.append("成熟")
    if any(word in description for word in ["神秘", "冷酷", "高冷"]):
        female_keywords.append("神秘")

    # 职业相关
    if any(word in description for word in ["律师", "医生", "教授", "总裁"]):
        female_keywords.append("精英")
    if any(word in description for word in ["艺术家", "音乐家", "作家"]):
        female_keywords.append("艺术气质")

    return ", ".join(female_keywords) if female_keywords else "魅力,吸引力"

# 其他缺失的模板
SCENE_IMAGE_PROMPT_OPTIMIZER_PROMPT = """
你是一位专业的场景设计师。请根据以下信息优化场景图片提示词。

**场景描述**: {{description}}
**女性偏好关键词**: {{female_appeal_keywords}}

{visual_conversion_rules}

请生成优化后的场景提示词：
""".format(visual_conversion_rules=VISUAL_CONVERSION_RULES)

SCENE_IMAGE_EVALUATION_PROMPT = """
你是一位严格的场景设计师。请为这张场景图片评分。

**场景描述**: {{description}}
**女性偏好关键词**: {{female_appeal_keywords}}

请返回JSON格式的评分：
{{{{
    "score": float
}}}}
"""

WORLDVIEW_EXPANSION_PROMPT = """
你是一位世界观设计师。请根据以下信息扩展世界观设定。

**基础设定**: {{base_setting}}
**故事背景**: {{story_background}}

请生成详细的世界观扩展：
"""

AGENT_BACKSTORY_PROMPT = """
你是一位角色背景故事创作师。请为以下角色创作详细的背景故事。

**角色信息**: {{character_info}}
**故事设定**: {{story_setting}}

请生成角色的详细背景故事：
"""

USER_REPLY_CHOICE_GENERATOR_PROMPT = """
你是一位对话选项设计师。请根据当前对话情境，生成3-4个用户回复选项。

**当前对话**: {{current_dialogue}}
**角色信息**: {{character_info}}
**情境**: {{situation}}

请生成用户回复选项：
"""

# ========================================
# 10. 织梦者引擎专用模板（小说改编核心）
# ========================================

# 互动场景生成模板 - 核心优化版
INTERACTIVE_SCENE_GENERATION_PROMPT = """
你是一位顶级的互动叙事设计师和小说游戏化工程师。你的任务是基于提供的上下文，将【小说原文片段】**精准地转化**成一个互动序列，**严格忠于原著**。

**核心原则：忠于原著**
1.  **原文优先**: 所有的 `narration` (旁白) 和 `dialogue` (对话) 元素，都**必须直接从【小说原文片段】中提取或进行最少量、不改变原意的精炼**。禁止进行任何形式的二次创作或风格模仿。
2.  **保留精髓**: 可以将大段的描述性文字拆分为多个 `narration` 节点，但必须保持原文的语序和核心信息。
3.  **合理植入选择**: `choice` 元素是你唯一可以添加的内容。它应该被植入在主角面临决策或内心挣扎的关键节点，选项内容应**严格基于原文中主角的内心独白或潜在的情绪反应**来设计。
4.  **禁止原创剧情**: 绝对禁止在互动序列中添加任何【小说原文片段】里没有的剧情、事件或对话。

**【上下文信息】**
- **上一章剧情回顾**: {previous_chapter_summary}
- **当前游戏状态**: {current_game_state}
- **本章摘要**: {chapter_summary}
- **本章关键互动**: {key_interactions}
- **AI需要扮演的角色索引映射**: {agent_index_map}
- **小说原文片段**:
---
{novel_chunk}
---

**最终输出格式 (最重要！)**

**CRITICAL RULE: The `interactive_sequence` array MUST always end with an element of `element_type: "choice"`. This is not optional. It provides the player with their first decision point for the chapter.**

请严格按照以下JSON格式返回你的转化结果，确保包含 `interactive_sequence` 和 `completion_summary` 两个顶级字段：
```json
{{
  "interactive_sequence": [
    {{
      "element_type": "narration",
      "content_or_prompt": "（直接从原文提取的旁白）"
    }},
    {{
      "element_type": "dialogue",
      "agent_index": 0,
      "content_or_prompt": "（直接从原文提取的角色对话）"
    }},
    {{
      "element_type": "choice",
      "content_or_prompt": "（基于主角内心活动设计的选择提示）",
      "choices": [
        {{
            "text": "（代表主角一种心声或可能反应的选项A）",
            "target_agent_index": 0
        }},
        {{
            "text": "（代表主角另一种心声或可能反应的选项B）",
            "target_agent_index": 1
        }}
      ]
    }}
  ],
  "completion_summary": "（这里是女主角在章节结束后的第一人称内心独白，必须基于本章情节进行总结）"
}}
```

现在，请开始你的转化工作。
"""

# 章节NPC剧本摘要生成模板 - 专用优化版
CHAPTER_SUMMARY_FOR_NPC_PROMPT = """
你是一位AI虚拟演员的导演。请为【所有参与本章的NPC角色】编写一份清晰、可执行的【行动剧本与表演指令】。

# 章节核心摘要:
{chapter_summary}

# 本章关键互动点:
{key_interactions}

# 你的任务:
生成一份剧本摘要，告诉每个NPC在本章的自由对话阶段：

1. **核心目标 (Objective)**: 他们在本章最想从主角那里获得什么信息，或者想达成什么情感目标？
   （例如：何以琛的目标是"试探赵默笙回国的真实目的，并压抑自己的情感"。）

2. **情绪基调 (Tone)**: 他们的主要情绪是什么？应该如何通过语言和非语言方式表达？
   （例如：何以琛的基调是"外表冷漠、克制，但偶尔会因特定话题流露出不易察觉的痛苦或在乎"。）

3. **关键行动/台词 (Key Actions/Lines)**: 在对话中，有哪些必须提及的关键信息或必须做出的关键行为？
   （例如："必须质问赵默笙关于'结婚'的事情"、"在对话中要刻意保持距离，避免身体接触"。）

4. **关系动态 (Relationship Dynamic)**: 他们当前如何看待主角？是警惕、是怀念、是怨恨，还是复杂的混合体？

请以简洁、直接的指令形式返回这份剧本，以便AI演员能准确地进行表演。
"""

# 高级故事聊天模板（用于prompt_assembler）
ADVANCED_STORY_CHAT_PROMPT = """
# 角色扮演指令
你正在扮演 {agent_name}。你的所有回应都必须严格遵循以下所有设定。

<persona>
{agent_persona}
</persona>

<world_info>
{world_info}
</world_info>

<chapter_context>
**章节标题**: {chapter_title}
**任务目标**: {mission_objective}
**关系状态**: {relationship_status}
**章节事件**: {chapter_event_summary}
</chapter_context>

<chat_history>
{chat_history}
</chat_history>

**当前用户输入**: {user_message}

<reply>
请以 {agent_name} 的身份回复。回复要自然、生动，符合角色设定和当前情境。
</reply>
"""

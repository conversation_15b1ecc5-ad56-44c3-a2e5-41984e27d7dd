"""
角色服务
专门处理角色创建、档案生成等功能
"""

import asyncio
import json
from typing import Optional, Tuple, Dict, Any

from .base_llm_service import BaseLLMService, retry_on_api_error
from .image_generation_service import ImageGenerationService
from ..pydantic_models import GeneratedCoreAgentData, ImageGenerationRequest
from ..prompt_templates import (
    get_core_agent_creation_prompt, get_archetype_agent_creation_prompt
)

class CharacterService(BaseLLMService):
    """角色服务"""
    
    def __init__(self):
        super().__init__()
        self.image_service = ImageGenerationService()
    
    @retry_on_api_error()
    async def generate_agent_from_analysis(self, character_gdd: Dict[str, Any],
                                         story_theme_summary: str) -> Tuple[GeneratedCoreAgentData, Optional[bytes], Optional[str]]:
        """
        根据角色GDD和故事主题摘要生成角色档案
        返回: (角色数据, 图片字节, 图片提示词)
        """
        character_name = character_gdd.get('name', '未知角色')
        print(f"INFO: 正在为角色 '{character_name}' 生成档案...")

        # 提取精准上下文
        archetype_info = character_gdd
        # 使用角色专属的关键场景描述作为核心的用户要求
        specific_user_prompt = archetype_info.get("key_scenes_description", story_theme_summary)

        # 选择合适的提示词模板
        archetype_name = archetype_info.get('name')
        if archetype_name:
            prompt = get_archetype_agent_creation_prompt(
                user_prompt=specific_user_prompt,
                archetype_name=archetype_name,
                core_temperament=archetype_info.get('personality_core', ""),
                behavior_pattern=archetype_info.get('emotional_appeal', ""),
                emotional_triggers="",  # 可选
                relationship_dynamic=""  # 可选
            )
        else:
            prompt = get_core_agent_creation_prompt(specific_user_prompt)

        # 使用 Instructor 生成并验证结构化角色数据
        agent_data: GeneratedCoreAgentData = await self.generate_structured_response(
            prompt=prompt,
            response_model=GeneratedCoreAgentData,
            model_name=self.role_model_name,
        )

        # 生成角色图片
        image_bytes, image_prompt = None, None
        try:
            result = await self.generate_image_from_description_with_prompt(agent_data.description)
            if result:
                image_bytes, image_prompt = result
        except Exception as e:
            print(f"WARN: 角色图片生成失败: {e}")

        return agent_data, image_bytes, image_prompt
    
    async def generate_image_from_description_with_prompt(self, description: str) -> Optional[Tuple[bytes, str]]:
        """根据描述生成角色图片"""
        print(f"INFO: 正在为描述 '{description[:50]}...' 生成形象图 (带智能重试机制)...")
        
        # 创建图片生成请求
        request = ImageGenerationRequest(
            description=description,
            image_type="character"
        )
        
        # 使用图片生成服务
        return await self.image_service.generate_enhanced_image_from_request(request)
    
    @retry_on_api_error()
    async def create_agent_profile(self, name: str, description: str, 
                                 personality: str, scenario: str,
                                 additional_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """创建完整的角色档案"""
        print(f"INFO: 正在创建角色 '{name}' 的完整档案...")
        
        # 基础档案数据
        profile = {
            "name": name,
            "description": description,
            "personality": personality,
            "scenario": scenario,
            "created_at": "now()",
            "updated_at": "now()"
        }
        
        # 添加额外数据
        if additional_data:
            profile.update(additional_data)
        
        # 生成角色图片
        try:
            image_result = await self.generate_image_from_description_with_prompt(description)
            if image_result:
                image_bytes, image_prompt = image_result
                profile["image_data"] = image_bytes
                profile["image_prompt"] = image_prompt
        except Exception as e:
            print(f"WARN: 角色图片生成失败: {e}")
        
        print(f"SUCCESS: 角色档案创建完成")
        return profile
    
    @retry_on_api_error()
    async def generate_character_variations(self, base_character: Dict[str, Any], 
                                          variation_count: int = 3) -> list[Dict[str, Any]]:
        """基于基础角色生成变体"""
        print(f"INFO: 正在为角色生成 {variation_count} 个变体...")
        
        variations = []
        base_description = base_character.get("description", "")
        
        for i in range(variation_count):
            variation_prompt = f"""
基于以下角色描述，创建一个相似但有所不同的角色变体：

原始角色: {base_description}

请生成第 {i+1} 个变体，保持核心特征但调整细节：
"""
            
            try:
                variation_data = await self.generate_json(variation_prompt, self.role_model_name)
                
                # 为变体生成图片
                if variation_data.get("description"):
                    image_result = await self.generate_image_from_description_with_prompt(
                        variation_data["description"]
                    )
                    if image_result:
                        image_bytes, image_prompt = image_result
                        variation_data["image_data"] = image_bytes
                        variation_data["image_prompt"] = image_prompt
                
                variations.append(variation_data)
                
            except Exception as e:
                print(f"WARN: 变体 {i+1} 生成失败: {e}")
        
        print(f"SUCCESS: 成功生成 {len(variations)} 个角色变体")
        return variations
    
    @retry_on_api_error()
    async def analyze_character_compatibility(self, character1: Dict[str, Any], 
                                            character2: Dict[str, Any]) -> Dict[str, Any]:
        """分析两个角色的兼容性"""
        print("INFO: 正在分析角色兼容性...")
        
        prompt = f"""
分析以下两个角色的兼容性和互动潜力：

角色1:
名称: {character1.get('name', 'Unknown')}
描述: {character1.get('description', '')}
性格: {character1.get('personality', '')}

角色2:
名称: {character2.get('name', 'Unknown')}
描述: {character2.get('description', '')}
性格: {character2.get('personality', '')}

请分析他们的：
1. 性格兼容性 (0-10分)
2. 互动潜力 (0-10分)
3. 冲突可能性 (0-10分)
4. 故事发展建议

返回JSON格式的分析结果。
"""
        
        analysis = await self.generate_json(prompt, self.role_model_name)
        
        print("SUCCESS: 角色兼容性分析完成")
        return analysis
    
    @retry_on_api_error()
    async def enhance_character_backstory(self, character: Dict[str, Any], 
                                        story_context: str = "") -> str:
        """增强角色背景故事"""
        print(f"INFO: 正在增强角色 '{character.get('name', 'Unknown')}' 的背景故事...")
        
        prompt = f"""
为以下角色创建详细的背景故事：

角色信息:
名称: {character.get('name', '')}
描述: {character.get('description', '')}
性格: {character.get('personality', '')}

故事背景: {story_context}

请创建一个详细的背景故事，包括：
1. 成长经历
2. 重要事件
3. 性格形成原因
4. 与故事背景的联系
5. 未来发展方向

返回详细的背景故事文本：
"""
        
        backstory = await self.generate_text(prompt, self.story_model_name)
        
        print(f"SUCCESS: 背景故事增强完成，长度: {len(backstory)}")
        return backstory
    
    def get_character_summary(self, character: Dict[str, Any]) -> str:
        """获取角色简要摘要"""
        name = character.get('name', 'Unknown')
        description = character.get('description', '')[:100]
        personality = character.get('personality', '')[:100]
        
        return f"{name}: {description}... 性格: {personality}..."
